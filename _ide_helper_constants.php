<?php
/**
 * IDE Helper for Dynamically Generated Constants
 * 
 * This file is auto-generated to help IDEs recognize dynamically created constants.
 * Generated on: 2025-06-30 19:53:37
 * 
 * DO NOT EDIT THIS FILE MANUALLY - it will be overwritten.
 * To regenerate: php system/generate_ide_helper.php
 */

// Prevent execution if included directly
if (!defined('FS_APP_ROOT')) {
    die('This file should not be executed directly.');
}

// ============================================================
// File System Paths
// ============================================================

/**
 * File system path to application root directory
 * 
 * @var string
 * @source fs_app_root
 */
if (!defined('FS_APP_ROOT')) define('FS_APP_ROOT', 'E:\\Build\\test.cadservices.co.uk\\baffletrain\\autocadlt\\autobooks/');

/**
 * File system document root path
 * 
 * @var string
 * @source fs_doc_root
 */
if (!defined('FS_DOC_ROOT')) define('FS_DOC_ROOT', '//');

/**
 * File system path to current application directory
 * 
 * @var string
 * @source fs_app
 */
if (!defined('FS_APP')) define('FS_APP', 'E:\\Build\\test.cadservices.co.uk\\baffletrain\\autocadlt\\autobooks/');

/**
 * File system path to resources directory
 * 
 * @var string
 * @source fs_resources
 */
if (!defined('FS_RESOURCES')) define('FS_RESOURCES', 'E:\\Build\\test.cadservices.co.uk\\baffletrain\\autocadlt\\autobooks/resources/');

/**
 * File system path to API directory
 * 
 * @var string
 * @source fs_api
 */
if (!defined('FS_API')) define('FS_API', 'E:\\Build\\test.cadservices.co.uk\\baffletrain\\autocadlt\\autobooks/resources/api/');

/**
 * File system path to classes directory
 * 
 * @var string
 * @source fs_classes
 */
if (!defined('FS_CLASSES')) define('FS_CLASSES', 'E:\\Build\\test.cadservices.co.uk\\baffletrain\\autocadlt\\autobooks/resources/classes/');

/**
 * File system path to functions directory
 * 
 * @var string
 * @source fs_functions
 */
if (!defined('FS_FUNCTIONS')) define('FS_FUNCTIONS', 'E:\\Build\\test.cadservices.co.uk\\baffletrain\\autocadlt\\autobooks/resources/functions/');

/**
 * File system path to views directory
 * 
 * @var string
 * @source fs_views
 */
if (!defined('FS_VIEWS')) define('FS_VIEWS', 'E:\\Build\\test.cadservices.co.uk\\baffletrain\\autocadlt\\autobooks/resources/views/');

/**
 * File system path to config directory
 * 
 * @var string
 * @source fs_config
 */
if (!defined('FS_CONFIG')) define('FS_CONFIG', 'E:\\Build\\test.cadservices.co.uk\\baffletrain\\autocadlt\\autobooks/resources/config/');

/**
 * File system path to templates directory
 * 
 * @var string
 * @source fs_templates
 */
if (!defined('FS_TEMPLATES')) define('FS_TEMPLATES', 'E:\\Build\\test.cadservices.co.uk\\baffletrain\\autocadlt\\autobooks/resources/templates/');

/**
 * File system path to components directory
 * 
 * @var string
 * @source fs_components
 */
if (!defined('FS_COMPONENTS')) define('FS_COMPONENTS', 'E:\\Build\\test.cadservices.co.uk\\baffletrain\\autocadlt\\autobooks/resources/components/');

/**
 * File system path to logs directory
 * 
 * @var string
 * @source fs_logs
 */
if (!defined('FS_LOGS')) define('FS_LOGS', 'E:\\Build\\test.cadservices.co.uk\\baffletrain\\autocadlt\\autobooks/resources/logs/');

/**
 * File system path to system directory
 * 
 * @var string
 * @source fs_system
 */
if (!defined('FS_SYSTEM')) define('FS_SYSTEM', 'E:\\Build\\test.cadservices.co.uk\\baffletrain\\autocadlt\\autobooks/system/');

/**
 * File system path to uploads directory
 * 
 * @var string
 * @source fs_uploads
 */
if (!defined('FS_UPLOADS')) define('FS_UPLOADS', 'E:\\Build\\test.cadservices.co.uk\\baffletrain\\autocadlt\\autobooks/uploads/');

/**
 * File system path to cache directory
 * 
 * @var string
 * @source fs_cache
 */
if (!defined('FS_CACHE')) define('FS_CACHE', '/var/www/vhosts/cadservices.co.uk/temp/autobooks');

/**
 * File system path to temporary files directory
 * 
 * @var string
 * @source fs_temp
 */
if (!defined('FS_TEMP')) define('FS_TEMP', '/var/www/vhosts/cadservices.co.uk/temp/autobooks');

/**
 * File system path to current application view path
 * 
 * @var string
 * @source fs_app_path
 */
if (!defined('FS_APP_PATH')) define('FS_APP_PATH', 'E:\\Build\\test.cadservices.co.uk\\baffletrain\\autocadlt\\autobooks/resources/views');


// ============================================================
// Input Parameters
// ============================================================

/**
 * Combined GET and POST parameters
 * 
 * @var array
 * @source input_params
 */
if (!defined('INPUT_PARAMS')) define('INPUT_PARAMS', array (
));


// ============================================================
// System Configuration
// ============================================================

/**
 * Array of system view names
 * 
 * @var array
 * @source system_views
 */
if (!defined('SYSTEM_VIEWS')) define('SYSTEM_VIEWS', array (
  0 => 'system',
  1 => 'login',
  2 => 'logout',
  3 => 'reset-password',
  4 => 'settings',
));


// ============================================================
// Request Information
// ============================================================

/**
 * Current request URI
 * 
 * @var string
 * @source request_uri
 */
if (!defined('REQUEST_URI')) define('REQUEST_URI', '');


// ============================================================
// Domain Information
// ============================================================

/**
 * Current domain name
 * 
 * @var string
 * @source domain
 */
if (!defined('DOMAIN')) define('DOMAIN', '');


// ============================================================
// Script Information
// ============================================================

/**
 * Current script filename
 * 
 * @var string
 * @source script_name
 */
if (!defined('SCRIPT_NAME')) define('SCRIPT_NAME', 'generate_constants_helper.php');


// ============================================================
// Document Root Paths
// ============================================================

/**
 * Document root path
 * 
 * @var string
 * @source doc_root
 */
if (!defined('DOC_ROOT')) define('DOC_ROOT', '//');


// ============================================================
// System File Paths
// ============================================================

/**
 * File system path to system API directory
 * 
 * @var string
 * @source fs_sys_api
 */
if (!defined('FS_SYS_API')) define('FS_SYS_API', 'E:\\Build\\test.cadservices.co.uk\\baffletrain\\autocadlt\\autobooks/system/api/');

/**
 * File system path to system classes directory
 * 
 * @var string
 * @source fs_sys_classes
 */
if (!defined('FS_SYS_CLASSES')) define('FS_SYS_CLASSES', 'E:\\Build\\test.cadservices.co.uk\\baffletrain\\autocadlt\\autobooks/system/classes/');

/**
 * File system path to system functions directory
 * 
 * @var string
 * @source fs_sys_functions
 */
if (!defined('FS_SYS_FUNCTIONS')) define('FS_SYS_FUNCTIONS', 'E:\\Build\\test.cadservices.co.uk\\baffletrain\\autocadlt\\autobooks/system/functions/');

/**
 * File system path to system views directory
 * 
 * @var string
 * @source fs_sys_views
 */
if (!defined('FS_SYS_VIEWS')) define('FS_SYS_VIEWS', 'E:\\Build\\test.cadservices.co.uk\\baffletrain\\autocadlt\\autobooks/system/views/');

/**
 * File system path to system config directory
 * 
 * @var string
 * @source fs_sys_config
 */
if (!defined('FS_SYS_CONFIG')) define('FS_SYS_CONFIG', 'E:\\Build\\test.cadservices.co.uk\\baffletrain\\autocadlt\\autobooks/system/config/');

/**
 * File system path to system templates directory
 * 
 * @var string
 * @source fs_sys_templates
 */
if (!defined('FS_SYS_TEMPLATES')) define('FS_SYS_TEMPLATES', 'E:\\Build\\test.cadservices.co.uk\\baffletrain\\autocadlt\\autobooks/system/templates/');

/**
 * File system path to system components directory
 * 
 * @var string
 * @source fs_sys_components
 */
if (!defined('FS_SYS_COMPONENTS')) define('FS_SYS_COMPONENTS', 'E:\\Build\\test.cadservices.co.uk\\baffletrain\\autocadlt\\autobooks/system/components/');

/**
 * File system path to system logs directory
 * 
 * @var string
 * @source fs_sys_logs
 */
if (!defined('FS_SYS_LOGS')) define('FS_SYS_LOGS', 'E:\\Build\\test.cadservices.co.uk\\baffletrain\\autocadlt\\autobooks/system/logs/');

/**
 * File system path to database class file
 * 
 * @var string
 * @source fs_sys_db_class
 */
if (!defined('FS_SYS_DB_CLASS')) define('FS_SYS_DB_CLASS', 'E:\\Build\\test.cadservices.co.uk\\baffletrain\\autocadlt\\autobooks/system/classes/database.class.php');


// ============================================================
// Application Paths
// ============================================================

/**
 * Web path to application root
 * 
 * @var string
 * @source app_root
 */
if (!defined('APP_ROOT')) define('APP_ROOT', '/E:Buildtest.cadservices.co.ukbaffletrainautocadltautobooks/');

/**
 * Current application path relative to root
 * 
 * @var string
 * @source app_path
 */
if (!defined('APP_PATH')) define('APP_PATH', '');


// ============================================================
// Path Components
// ============================================================

/**
 * Array of path segments
 * 
 * @var array
 * @source path_parts
 */
if (!defined('PATH_PARTS')) define('PATH_PARTS', array (
  0 => '',
));


// ============================================================
// Navigation Information
// ============================================================

/**
 * Top-level path segment
 * 
 * @var string
 * @source top_level
 */
if (!defined('TOP_LEVEL')) define('TOP_LEVEL', '');


// ============================================================
// Current Context
// ============================================================

/**
 * Current page name
 * 
 * @var string
 * @source current_page
 */
if (!defined('CURRENT_PAGE')) define('CURRENT_PAGE', '');


// ============================================================
// Combo File System Paths
// ============================================================

/**
 * File system path combining app root and current application path
 * 
 * @var string
 * @source fs_full_path
 */
if (!defined('FS_FULL_PATH')) define('FS_FULL_PATH', 'E:\\Build\\test.cadservices.co.uk\\baffletrain\\autocadlt\\autobooks');

/**
 * File system path to current page including full application path
 * 
 * @var string
 * @source fs_full_page
 */
if (!defined('FS_FULL_PAGE')) define('FS_FULL_PAGE', 'E:\\Build\\test.cadservices.co.uk\\baffletrain\\autocadlt\\autobooks/resources/views');


// ============================================================
// Combo Web Paths
// ============================================================

/**
 * Web path combining app root and current application path
 * 
 * @var string
 * @source full_path
 */
if (!defined('FULL_PATH')) define('FULL_PATH', '/E:Buildtest.cadservices.co.ukbaffletrainautocadltautobooks/');

/**
 * Web path to current page including full application path
 * 
 * @var string
 * @source full_page
 */
if (!defined('FULL_PAGE')) define('FULL_PAGE', '/E:Buildtest.cadservices.co.ukbaffletrainautocadltautobooks/');


// ============================================================
// Path Source Information
// ============================================================

/**
 * Indicates how the path was determined
 * 
 * @var string
 * @source set_by
 */
if (!defined('SET_BY')) define('SET_BY', 'default');


// ============================================================
// HTMX Source Information
// ============================================================

/**
 * HTMX source path
 * 
 * @var string
 * @source source_path
 */
if (!defined('SOURCE_PATH')) define('SOURCE_PATH', '');

/**
 * HTMX source page
 * 
 * @var string
 * @source source_page
 */
if (!defined('SOURCE_PAGE')) define('SOURCE_PAGE', '');

/**
 * HTMX source path components
 * 
 * @var string
 * @source source_path_parts
 */
if (!defined('SOURCE_PATH_PARTS')) define('SOURCE_PATH_PARTS', '');

/**
 * HTMX source application path
 * 
 * @var string
 * @source source_app_path
 */
if (!defined('SOURCE_APP_PATH')) define('SOURCE_APP_PATH', '');


