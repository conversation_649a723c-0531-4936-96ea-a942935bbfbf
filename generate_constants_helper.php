<?php
/**
 * Quick Constants Helper Generator
 *
 * This script generates the IDE helper file by setting the $generate_ide_helper flag
 * and running through the normal startup sequence.
 *
 * Usage: php generate_constants_helper.php
 */

/**
 * Generate IDE helper file for constants
 *
 * @param array $constants_defined Array of constants with metadata
 * @return void
 */
function generate_ide_helper_constants($constants_defined): void {
    $app_root = $constants_defined['FS_APP_ROOT']['value'] ?? dirname(__DIR__) . '/';
    $helper_file = $app_root . '_ide_helper_constants.php';

    // Load path definitions
    $definitions_file = $app_root . 'system/config/path_definitions.php';
    $definitions = file_exists($definitions_file) ? include($definitions_file) : [];

    // Load existing constants from schema to get descriptions
    $schema_file = $app_root . 'system/config/path_schema.php';
    $schema = file_exists($schema_file) ? include($schema_file) : [];

    // Build file header using definitions
    $header = $definitions['ide_helper']['file_header'] ?? [];
    $safety = $definitions['ide_helper']['safety_check'] ?? [];

    $content = "<?php\n";
    $content .= "/**\n";
    $content .= " * " . ($header['title'] ?? 'IDE Helper for Dynamically Generated Constants') . "\n";
    $content .= " * \n";
    $content .= " * " . ($header['description'] ?? 'This file is auto-generated to help IDEs recognize dynamically created constants.') . "\n";
    $content .= " * Generated on: " . date('Y-m-d H:i:s') . "\n";
    $content .= " * \n";
    $content .= " * " . ($header['warning'] ?? 'DO NOT EDIT THIS FILE MANUALLY - it will be overwritten.') . "\n";
    $content .= " * " . ($header['regenerate_command'] ?? 'To regenerate: php system/generate_ide_helper.php') . "\n";
    $content .= " */\n\n";

    $content .= "// Prevent execution if included directly\n";
    $condition = $safety['condition'] ?? "!defined('FS_APP_ROOT')";
    $message = $safety['message'] ?? 'This file should not be executed directly.';
    $content .= "if ($condition) {\n";
    $content .= "    die('$message');\n";
    $content .= "}\n\n";

    // Group constants by category for better organization
    $categories = $definitions['categories'] ?? [
        'FS_' => 'File System Paths',
        'APP_' => 'Application Paths',
        'OTHER' => 'Other Constants'
    ];

    $categorized_constants = [];
    $other_constants = [];

    foreach ($constants_defined as $name => $info) {
        $categorized = false;
        foreach ($categories as $prefix => $category) {
            if (str_starts_with($name, $prefix)) {
                $categorized_constants[$category][$name] = $info;
                $categorized = true;
                break;
            }
        }
        if (!$categorized) {
            $other_constants[$name] = $info;
        }
    }

    // Generate constants by category
    $separator = $definitions['ide_helper']['section_separator'] ?? str_repeat('=', 60);

    foreach ($categorized_constants as $category => $constants) {
        $content .= "// $separator\n";
        $content .= "// $category\n";
        $content .= "// $separator\n\n";

        foreach ($constants as $name => $info) {
            $content .= generate_constant_definition($name, $info, $schema, $definitions);
        }
        $content .= "\n";
    }

    // Generate other constants
    if (!empty($other_constants)) {
        $content .= "// $separator\n";
        $content .= "// Other Constants\n";
        $content .= "// $separator\n\n";

        foreach ($other_constants as $name => $info) {
            $content .= generate_constant_definition($name, $info, $schema, $definitions);
        }
    }

    // Write the file
    file_put_contents($helper_file, $content);
}

/**
 * Generate a single constant definition with documentation
 *
 * @param string $name Constant name
 * @param array $info Constant information
 * @param array $schema Path schema for descriptions
 * @param array $definitions Path definitions configuration
 * @return string
 */
function generate_constant_definition($name, $info, $schema, $definitions = []): string {
    $value = $info['value'];
    $type = $info['type'];
    $source_key = $info['source_key'];

    // Generate description based on constant name and schema
    $description = generate_constant_description($name, $source_key, $schema, $definitions);

    $content = "/**\n";
    $content .= " * $description\n";
    $content .= " * \n";
    $content .= " * @var $type\n";
    $content .= " * @source $source_key\n";
    $content .= " */\n";

    // Format the value for the define statement
    if (is_string($value)) {
        $formatted_value = "'" . addslashes($value) . "'";
    } elseif (is_bool($value)) {
        $formatted_value = $value ? 'true' : 'false';
    } elseif (is_null($value)) {
        $formatted_value = 'null';
    } else {
        $formatted_value = var_export($value, true);
    }

    $content .= "if (!defined('$name')) define('$name', $formatted_value);\n\n";

    return $content;
}

/**
 * Generate description for a constant based on its name and source
 *
 * @param string $name Constant name
 * @param string $source_key Source key from path array
 * @param array $schema Path schema
 * @param array $definitions Path definitions configuration
 * @return string
 */
function generate_constant_description($name, $source_key, $schema, $definitions = []): string {
    // Load descriptions from definitions file
    $descriptions = $definitions['descriptions'] ?? [];

    // Check for specific description first
    if (isset($descriptions[$name])) {
        return $descriptions[$name];
    }

    // Generate description based on patterns from definitions
    $patterns = $definitions['patterns'] ?? [];

    foreach ($patterns as $prefix => $template) {
        if (str_starts_with($name, $prefix)) {
            $part = strtolower(substr($name, strlen($prefix)));
            return str_replace('{part}', $part, $template);
        }
    }

    // Default description
    $default_template = $definitions['default_description'] ?? 'Dynamically generated constant from {source_key}';
    return str_replace('{source_key}', $source_key, $default_template);
}

echo "Generating IDE Helper for Constants...\n";

// Set the flag to generate IDE helper
$generate_ide_helper = true;

// Initialize path
$path = [];
$path['fs_app_root'] = __DIR__ . '/';

// Load the path schema
$schema_file = $path['fs_app_root'] . 'system/config/path_schema.php';
$schema = file_exists($schema_file) ? include($schema_file) : [];

// Include paths functions
include($path['fs_app_root'] . "system/paths.php");

// Build paths with schema
$path = build_paths($path, $schema);

// Generate constants with IDE helper
build_constants($path, $generate_ide_helper);

echo "✓ IDE helper file generated successfully!\n";
echo "✓ File location: _ide_helper_constants.php\n";
echo "\nYour IDE should now recognize all dynamically generated constants.\n";
echo "Re-run this script whenever you add new constants or change paths.\n";
?>
