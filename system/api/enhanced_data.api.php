<?php
namespace api\enhanced_data;

use system\data_importer;
use system\data_table_generator;
use system\table_config_manager;
use system\database;

/**
 * Enhanced Data API
 * 
 * Handles CSV upload, analysis, and table creation with automatic schema detection
 */

function upload_csv($p) {
    $table_name = $p['table_name'] ?? 'autobooks_enhanced_data';
    $route_key = $p['route_key'] ?? 'enhanced_data';
    $auto_schema = ($p['auto_schema'] ?? '0') === '1';
    $replace_table = ($p['replace_table'] ?? '0') === '1';
    
    try {
        // Validate file upload
        if (!isset($_FILES['csv_file']) || $_FILES['csv_file']['error'] !== UPLOAD_ERR_OK) {
            return generate_error_response('No CSV file uploaded or upload error occurred');
        }
        
        $csv_file_path = $_FILES['csv_file']['tmp_name'];
        $original_filename = $_FILES['csv_file']['name'];
        
        tcs_log([
            'action' => 'csv_upload_started',
            'table_name' => $table_name,
            'filename' => $original_filename,
            'auto_schema' => $auto_schema,
            'replace_table' => $replace_table
        ], 'enhanced_data_api');
        
        if ($auto_schema) {
            // Use enhanced import with automatic schema detection
            $result = data_importer::import_csv_with_auto_schema(
                $csv_file_path, 
                $table_name, 
                true, 
                $replace_table
            );
            
            if (isset($result['error'])) {
                return generate_error_response($result['error']);
            }
            
            // Generate success response with analysis details
            return generate_success_response_with_analysis($result, $table_name);
            
        } else {
            // Use simple hilt table import
            $result = data_importer::import_csv_to_hilt_table(
                $table_name, 
                $csv_file_path, 
                true, 
                $replace_table
            );
            
            if (isset($result['error'])) {
                return generate_error_response($result['error']);
            }
            
            return generate_simple_success_response($result, $table_name);
        }
        
    } catch (\Exception $e) {
        tcs_log([
            'action' => 'csv_upload_error',
            'table_name' => $table_name,
            'error' => $e->getMessage()
        ], 'enhanced_data_api');
        
        return generate_error_response('Upload failed: ' . $e->getMessage());
    }
}

function analyze_csv($p) {
    try {
        // Validate file upload
        if (!isset($_FILES['csv_file']) || $_FILES['csv_file']['error'] !== UPLOAD_ERR_OK) {
            return generate_error_response('No CSV file uploaded or upload error occurred');
        }
        
        $csv_file_path = $_FILES['csv_file']['tmp_name'];
        $sample_rows = (int)($p['sample_rows'] ?? 100);
        
        // Analyze CSV structure
        $analysis = data_importer::analyze_csv_structure($csv_file_path, $sample_rows);
        
        if (isset($analysis['error'])) {
            return generate_error_response($analysis['error']);
        }
        
        return generate_analysis_response($analysis);
        
    } catch (\Exception $e) {
        return generate_error_response('Analysis failed: ' . $e->getMessage());
    }
}

function get_table_data($p) {
    $table_name = $p['table_name'] ?? 'autobooks_enhanced_data';
    $criteria = build_criteria_from_params($p);
    $options = build_options_from_params($p);

    try {
        // Use stored configuration for performance
        $result = table_config_manager::get_updated_config_with_data($table_name, $criteria, $options);

        if (isset($result['error'])) {
            // Fallback to generating config if stored config not found
            $fallback_result = data_table_generator::generate_table_config($table_name, $criteria, $options);
            if (isset($fallback_result['error'])) {
                return generate_error_response($fallback_result['error']);
            }
            $result = $fallback_result;
        }

        // Return just the table body if requested
        if ($options['just_body']) {
            return data_table_generator::render_data_table($result['config']);
        }

        return $result['config'];

    } catch (\Exception $e) {
        return generate_error_response('Failed to retrieve table data: ' . $e->getMessage());
    }
}

function data_table_filter($p) {
    // Process data table filter request
    $table_name = $p['table_name'] ?? 'autobooks_enhanced_data';
    $criteria = build_criteria_from_params($p);
    $options = build_options_from_params($p);
    $options['just_body'] = true; // Return only table body for HTMX updates
    
    return get_table_data(array_merge($p, ['just_body' => true]));
}

// Helper functions

function build_criteria_from_params($p): array {
    $criteria = [];
    
    // Search
    if (!empty($p['search'])) {
        $criteria['search'] = $p['search'];
    }
    
    // Pagination
    $page = (int)($p['page'] ?? 1);
    $per_page = (int)($p['per_page'] ?? 50);
    $criteria['limit'] = $per_page;
    $criteria['offset'] = ($page - 1) * $per_page;
    
    // Sorting
    if (!empty($p['sort_column'])) {
        $criteria['order_by'] = $p['sort_column'];
        $criteria['order_direction'] = $p['sort_direction'] ?? 'asc';
    }
    
    // Filters
    if (isset($p['filters']) && is_array($p['filters'])) {
        $criteria['where'] = $p['filters'];
    }
    
    return $criteria;
}

function build_options_from_params($p): array {
    return [
        'title' => $p['title'] ?? 'Enhanced Data Table',
        'description' => $p['description'] ?? '',
        'items_per_page' => (int)($p['per_page'] ?? 50),
        'current_page_num' => (int)($p['page'] ?? 1),
        'sort_column' => $p['sort_column'] ?? '',
        'sort_direction' => $p['sort_direction'] ?? 'asc',
        'callback' => 'data_table_filter',
        'class' => $p['table_class'] ?? 'enhanced-data-table',
        'just_body' => ($p['just_body'] ?? '0') === '1',
        'just_rows' => ($p['just_rows'] ?? '0') === '1',
        'show_system_columns' => ($p['show_system_columns'] ?? '0') === '1',
        'hidden_columns' => explode(',', $p['hidden_columns'] ?? 'data_hash'),
        'column_labels' => json_decode($p['column_labels'] ?? '{}', true) ?: []
    ];
}

function generate_error_response(string $message): string {
    return '<div class="bg-red-50 border border-red-200 rounded-md p-4">
        <div class="flex">
            <div class="flex-shrink-0">
                <i class="fas fa-exclamation-circle text-red-400"></i>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-red-800">Error</h3>
                <div class="mt-2 text-sm text-red-700">' . htmlspecialchars($message) . '</div>
            </div>
        </div>
    </div>';
}

function generate_success_response_with_analysis($result, string $table_name): string {
    $analysis = $result['analysis'];
    $schema = $result['schema'];
    $import_result = $result['import_result'];
    $config_result = $result['config_result'] ?? null;

    $imported_count = $import_result['imported_count'] ?? 0;
    $failed_count = $import_result['failed_count'] ?? 0;

    $html = '<div class="bg-green-50 border border-green-200 rounded-md p-4">
        <div class="flex">
            <div class="flex-shrink-0">
                <i class="fas fa-check-circle text-green-400"></i>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-green-800">Enhanced Import Successful</h3>
                <div class="mt-2 text-sm text-green-700">
                    <p>Table "' . htmlspecialchars($table_name) . '" created with typed columns and stored configuration.</p>
                    <div class="mt-3 grid grid-cols-4 gap-4 text-center">
                        <div>
                            <div class="text-lg font-bold text-green-600">' . count($analysis['headers']) . '</div>
                            <div class="text-xs">Typed Columns</div>
                        </div>
                        <div>
                            <div class="text-lg font-bold text-green-600">' . $imported_count . '</div>
                            <div class="text-xs">Rows Imported</div>
                        </div>
                        <div>
                            <div class="text-lg font-bold text-green-600">' . count(array_unique(array_values($analysis['data_types']))) . '</div>
                            <div class="text-xs">Data Types</div>
                        </div>
                        <div>
                            <div class="text-lg font-bold text-blue-600"><i class="fas fa-database"></i></div>
                            <div class="text-xs">Config Stored</div>
                        </div>
                    </div>';

    if ($failed_count > 0) {
        $html .= '<div class="mt-2 text-xs text-yellow-600">
            <i class="fas fa-exclamation-triangle"></i> ' . $failed_count . ' rows failed to import
        </div>';
    }

    $html .= '<div class="mt-3 text-xs">
                        <strong>Key Features:</strong> Individual typed columns, stored configuration for performance, automatic schema detection
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="mt-4">
        <button onclick="window.location.reload()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium">
            <i class="fas fa-table mr-2"></i>View Enhanced Data Table
        </button>
    </div>';

    return $html;
}

function generate_simple_success_response($result, string $table_name): string {
    return '<div class="bg-green-50 border border-green-200 rounded-md p-4">
        <div class="flex">
            <div class="flex-shrink-0">
                <i class="fas fa-check-circle text-green-400"></i>
            </div>
            <div class="ml-3">
                <h3 class="text-sm font-medium text-green-800">Success</h3>
                <div class="mt-2 text-sm text-green-700">
                    <p>' . htmlspecialchars($result['message']) . '</p>
                    <div class="mt-3">
                        <button onclick="window.location.reload()" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                            <i class="fas fa-table mr-2"></i>View Data Table
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>';
}

function generate_analysis_response($analysis): string {
    $html = '<div class="bg-blue-50 border border-blue-200 rounded-md p-4">
        <h3 class="text-lg font-medium text-blue-900 mb-3">CSV Analysis Results</h3>
        
        <div class="grid grid-cols-3 gap-4 mb-4 text-center">
            <div>
                <div class="text-2xl font-bold text-blue-600">' . count($analysis['headers']) . '</div>
                <div class="text-sm text-gray-600">Columns</div>
            </div>
            <div>
                <div class="text-2xl font-bold text-blue-600">' . $analysis['total_rows'] . '</div>
                <div class="text-sm text-gray-600">Total Rows</div>
            </div>
            <div>
                <div class="text-2xl font-bold text-blue-600">' . $analysis['analyzed_rows'] . '</div>
                <div class="text-sm text-gray-600">Analyzed Rows</div>
            </div>
        </div>
        
        <div class="overflow-x-auto">
            <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                    <tr>
                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Column</th>
                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Type</th>
                        <th class="px-4 py-2 text-left text-xs font-medium text-gray-500 uppercase">Sample</th>
                    </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">';
    
    foreach ($analysis['headers'] as $header) {
        $type = $analysis['data_types'][$header];
        $sample = implode(', ', array_slice($analysis['sample_data'][$header], 0, 2));
        
        $html .= '<tr>
            <td class="px-4 py-2 text-sm font-medium text-gray-900">' . htmlspecialchars($header) . '</td>
            <td class="px-4 py-2 text-sm text-gray-500">
                <span class="inline-flex items-center px-2 py-0.5 rounded text-xs font-medium bg-blue-100 text-blue-800">' . $type . '</span>
            </td>
            <td class="px-4 py-2 text-sm text-gray-500">' . htmlspecialchars($sample) . '</td>
        </tr>';
    }
    
    $html .= '</tbody></table></div></div>';
    
    return $html;
}
