<?php

namespace system;
use PDO;
use PDOStatement;
use PDOException;
use Exception;
use const FS_APP_ROOT;

/**
 * Custom exception for database errors with additional context
 */
class DatabaseException extends Exception {
    private string $query;
    private array $params;

    public function __construct(string $message, int $code = 0, Exception $previous = null, string $query = '', array $params = []) {
        parent::__construct($message, $code, $previous);
        $this->query = $query;
        $this->params = $params;
    }

    public function getQuery(): string {
        return $this->query;
    }

    public function getParams(): array {
        return $this->params;
    }

    public function getDetailedMessage(): string {
        return $this->getMessage() . " | Query: " . $this->query . " | Params: " . json_encode($this->params);
    }
}

class database {
    private static ?database $instance = null;
    private ?PDO $pdo;
    private string $table;
    private string $select = '*';
    private array $where = [];
    private array $orderBy = [];
    private ?int $limit = null;
    private ?int $offset = null;
    private array $joins = [];
    private array $casts = [];

    private function __construct(PDO $pdo) {
        $this->pdo = $pdo;
    }

    public static function connection(?\PDO $connection = null): database {
        if (!$connection) {
            // Include the database configuration file
              require_once(FS_SYSTEM . "config/db_config.php");

            // Ensure variables are in scope
            global $db_server, $db_username, $db_password, $db_database;

            // If variables are still not set, try to get them directly
            if (empty($db_username) || empty($db_database)) {
                $tcs_database = str_replace('.', '', DOMAIN);
                $db_server = 'localhost';
                $db_username = $tcs_database ?? 'wwwcadservicescouk';
                $db_password = 'S96#1kvYuCGE';
                $db_database = $tcs_database ?? 'wwwcadservicescouk';
            }

            try {
                // Try to connect using TCP/IP
                $dsn = "mysql:host=127.0.0.1;dbname=$db_database;charset=utf8";
                $options = [
                    PDO::ATTR_ERRMODE            => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::ATTR_EMULATE_PREPARES   => false,
                    PDO::ATTR_TIMEOUT            => 5,
                ];

                $connection = new PDO($dsn, $db_username, $db_password, $options);
                $connection->exec("set session sql_mode=''");
            } catch (PDOException $e) {
                // If TCP/IP fails, try with localhost
                try {
                    $dsn = "mysql:host=localhost;dbname=$db_database;charset=utf8";
                    $connection = new PDO($dsn, $db_username, $db_password, $options);
                    $connection->exec("set session sql_mode=''");
                } catch (PDOException $e2) {
                    die('Connection failed: ' . $e2->getMessage() . ' (Original error: ' . $e->getMessage() . ')' .
                        ' [Username: ' . (empty($db_username) ? 'EMPTY' : 'SET') .
                        ', Database: ' . (empty($db_database) ? 'EMPTY' : $db_database) . ']');
                }
            }
        }

        if (self::$instance === null) {
            self::$instance = new self($connection);
        }

        return self::$instance;
    }



    public static function table(string $table): database {
        $instance = self::connection();
        $instance->table = $table;
        $instance->reset();
        return $instance;
    }

    public function select(array|string $columns): database {
        $this->select = is_array($columns) ? implode(', ', $columns) : $columns;
        return $this;
    }

    public function where(array|string $column, mixed $operator = null, mixed $value = null): database {
        if (is_array($column)) {
            foreach ($column as $key => $val) {
                $this->where[] = [$key, '=', $val];
            }
            return $this;
        }

        if ($value === null) {
            $value = $operator;
            $operator = '=';
        }

        $this->where[] = [$column, $operator, $value];
        return $this;
    }

    public function orderBy(string $column, string $direction = 'asc'): database {
        $this->orderBy[] = [$column, $direction];
        return $this;
    }

    public function limit(int $limit): database {
        $this->limit = $limit;
        return $this;
    }

    public function offset(int $offset): database {
        $this->offset = $offset;
        return $this;
    }

    public function join(string $table, string $first, string $operator = null, string $second = null): database {
        $this->joins[] = ['inner', $table, $first, $operator, $second];
        return $this;
    }

    public function leftJoin(string $table, string $first, string $operator = null, string $second = null): database {
        $this->joins[] = ['left', $table, $first, $operator, $second];
        return $this;
    }

    public function cast(array $columns): database {
        $this->casts = $columns;
        return $this;
    }

    private function applyCasts(array $results): array {
        if (empty($this->casts)) {
            return $results;
        }

        foreach ($results as &$row) {
            foreach ($this->casts as $column => $type) {
                if (isset($row[$column])) {
                    switch ($type) {
                        case 'int':
                        case 'integer':
                            $row[$column] = (int)$row[$column];
                            break;
                        case 'float':
                        case 'double':
                            $row[$column] = (float)$row[$column];
                            break;
                        case 'bool':
                        case 'boolean':
                            $row[$column] = (bool)$row[$column];
                            break;
                        case 'json':
                        case 'array':
                            // Handle both native JSON columns and TEXT-based JSON storage
                            if (is_string($row[$column])) {
                                // Data is stored as string (either TEXT fallback or JSON column returned as string)
                                $decoded = json_decode($row[$column], true);
                                if (json_last_error() === JSON_ERROR_NONE) {
                                    $row[$column] = $decoded;
                                } else {
                                    // If JSON decode fails, log the error and keep original value
                                    if (function_exists('tcs_log')) {
                                        tcs_log([
                                            'column' => $column,
                                            'value' => $row[$column],
                                            'json_error' => json_last_error_msg(),
                                            'action' => 'keeping_original_value'
                                        ], 'database_json_cast_errors');
                                    }
                                }
                            } elseif (is_array($row[$column]) || is_object($row[$column])) {
                                // Data is already decoded (shouldn't happen with MySQL but handle gracefully)
                                $row[$column] = is_object($row[$column]) ? (array)$row[$column] : $row[$column];
                            }
                            // If it's null or other type, leave as-is
                            break;
                        case 'date':
                            $row[$column] = new \DateTime($row[$column]);
                            break;
                    }
                }
            }
        }

        return $results;
    }

    public function get(): array {
        $query = $this->buildQuery();
        $stmt = $this->executeQuery($query, $this->getBindings());
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        return $this->applyCasts($results);
    }

    public function exists(): bool {
        $query = $this->buildQuery();
        $stmt = $this->executeQuery($query, $this->getBindings());
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result !== false;
    }

    public function first(): ?array {
        $this->limit(1);
        $results = $this->get();
        return !empty($results) ? $results[0] : null;
    }

    public function count(): int {
        $query = $this->buildCountQuery();
        $stmt = $this->executeQuery($query, $this->getBindings());
        return (int) $stmt->fetchColumn();
    }

    public function insert(array $data): PDOStatement {
        // Check if this is a batch insert (array of arrays)
        if (isset($data[0]) && is_array($data[0])) {
            // Handle batch insert
            $batch = $data;
            $stmt = null;

            foreach ($batch as $row) {
                $stmt = $this->insert($row);
            }

            return $stmt; // Return the last statement
        }

        // Check if we have numeric keys
        if (array_keys($data) === range(0, count($data) - 1)) {
            // Get the table columns from the database
            try {
                $query = "SHOW COLUMNS FROM {$this->table}";
                $stmt = $this->pdo->query($query);
                $columns = $stmt->fetchAll(PDO::FETCH_COLUMN, 0);

                // Map numeric array to column names
                if (count($columns) >= count($data)) {
                    $mappedData = [];
                    foreach ($data as $index => $value) {
                        $mappedData[$columns[$index]] = $value;
                    }
                    $data = $mappedData;
                } else {
                    die('Error: Not enough columns in table to match the numeric array. Please provide an associative array with column names as keys.');
                }
            } catch (PDOException $e) {
                die('Error: Failed to get table columns. ' . $e->getMessage());
            }
        }

        $columns = implode(', ', array_map(function($col) {
            return "`$col`"; // Wrap column names in backticks
        }, array_keys($data)));

        // Create named parameters that match column names
        $namedParams = [];
        $placeholders = [];
        foreach (array_keys($data) as $column) {
            $paramName = $this->generateColumnParamName($column);
            $placeholders[] = $paramName;
            $namedParams[$paramName] = $data[$column];
        }

        $placeholderString = implode(', ', $placeholders);
        $query = "INSERT INTO {$this->table} ($columns) VALUES ($placeholderString)";

        return $this->executeQuery($query, $namedParams);
    }

    public function update(array $data): PDOStatement {
        $set = '';
        $namedParams = [];

        // Create named parameters for SET clause that match column names
        foreach ($data as $column => $value) {
            $paramName = $this->generateColumnParamName($column, 'set');
            $set .= "`$column` = $paramName, ";
            $namedParams[$paramName] = $value;
        }
        $set = rtrim($set, ', ');

        $whereClause = $this->buildWhereClause();
        $query = "UPDATE {$this->table} SET $set WHERE $whereClause";

        // Merge SET parameters with WHERE parameters
        $whereParams = $this->getBindings();
        $allParams = array_merge($namedParams, $whereParams);

        return $this->executeQuery($query, $allParams);
    }

    public function delete(): PDOStatement {
        $whereClause = $this->buildWhereClause();
        $query = "DELETE FROM {$this->table} WHERE {$whereClause}";
        return $this->executeQuery($query, $this->getBindings());
    }
    public function truncate(): PDOStatement {
        $query = "TRUNCATE TABLE {$this->table}";
        return $this->executeQuery($query);
    }

    private function buildQuery(): string {
        $query = "SELECT {$this->select} FROM {$this->table}";

        // Add joins
        foreach ($this->joins as $join) {
            list($type, $table, $first, $operator, $second) = $join;
            $joinType = $type === 'left' ? 'LEFT JOIN' : 'JOIN';
            $query .= " {$joinType} {$table} ON {$first} {$operator} {$second}";
        }

        // Add where conditions
        if (!empty($this->where)) {
            $query .= " WHERE " . $this->buildWhereClause();
        }

        // Add order by
        if (!empty($this->orderBy)) {
            $query .= " ORDER BY ";
            $orderClauses = [];
            foreach ($this->orderBy as $order) {
                $orderClauses[] = "{$order[0]} " . strtoupper($order[1]);
            }
            $query .= implode(', ', $orderClauses);
        }

        // Add limit and offset
        if ($this->limit !== null) {
            $query .= " LIMIT {$this->limit}";
            if ($this->offset !== null) {
                $query .= " OFFSET {$this->offset}";
            }
        }

        return $query;
    }

    private function buildCountQuery(): string {
        $query = "SELECT COUNT(*) FROM {$this->table}";

        // Add joins
        foreach ($this->joins as $join) {
            list($type, $table, $first, $operator, $second) = $join;
            $joinType = $type === 'left' ? 'LEFT JOIN' : 'JOIN';
            $query .= " {$joinType} {$table} ON {$first} {$operator} {$second}";
        }

        // Add where conditions
        if (!empty($this->where)) {
            $query .= " WHERE " . $this->buildWhereClause();
        }

        // Note: COUNT queries don't need ORDER BY, LIMIT, or OFFSET
        // as we're only interested in the total count

        return $query;
    }

    private function buildWhereClause(): string {
        $conditions = [];
        foreach ($this->where as $index => $condition) {
            list($column, $operator, $value) = $condition;
            $paramName = $this->generateColumnParamName($column, 'where', $index);
            $conditions[] = "`{$column}` {$operator} {$paramName}";
        }
        return implode(' AND ', $conditions);
    }

    private function getBindings(): array {
        $bindings = [];
        foreach ($this->where as $index => $condition) {
            $paramName = $this->generateColumnParamName($condition[0], 'where', $index);
            $bindings[$paramName] = $condition[2];
        }
        return $bindings;
    }

    private function getBindingValues(): array {
        $values = [];
        foreach ($this->where as $condition) {
            $values[] = $condition[2];
        }
        return $values;
    }

    /**
     * Generate a named parameter that matches the column name
     *
     * @param string $column The column name
     * @param string $context The context (insert, set, where, etc.)
     * @param int|null $index Optional index for uniqueness
     * @return string The named parameter (e.g., :column_name, :set_column_name, :where_column_name_0)
     */
    private function generateColumnParamName(string $column, string $context = '', ?int $index = null): string {
        // Clean column name to be safe for parameter names
        $cleanColumn = preg_replace('/[^a-zA-Z0-9_]/', '_', $column);

        // Build parameter name
        $paramName = ':';
        if (!empty($context)) {
            $paramName .= $context . '_';
        }
        $paramName .= $cleanColumn;

        // Add index if provided for uniqueness
        if ($index !== null) {
            $paramName .= '_' . $index;
        }

        return $paramName;
    }

    private function reset(): database {
        $this->select = '*';
        $this->where = [];
        $this->orderBy = [];
        $this->limit = null;
        $this->offset = null;
        $this->joins = [];
        return $this;
    }

    private function executeQuery(string $query, array $params = []): PDOStatement|false {
        try {
            $this->logQuery($query, $params);
            $stmt = $this->pdo->prepare($query);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            // Log detailed error information
            $this->handle_database_error($e, $query, $params);
        }
        return false;
    }

    /**
     * Log database queries for debugging
     */
    private function logQuery(string $query, array $params = []): void {
        if (defined('STORE_DB_TRANSACTIONS') && STORE_DB_TRANSACTIONS === 'true') {
            $logData = [
                'query' => $query,
                'params' => $params,
                'timestamp' => date('Y-m-d H:i:s'),
                'table' => $this->table ?? 'Unknown'
            ];

            if (function_exists('tcs_log')) {
                tcs_log($logData, 'database_queries', true);
            } else {
                // Fallback logging
                error_log('DATABASE QUERY: ' . $query . "\nPARAMS: " . json_encode($params) . "\n", 3, STORE_PAGE_PARSE_TIME_LOG ?? 'php://stderr');
            }
        }
    }

    /**
     * Log detailed database error information
     */
    private function handle_database_error(PDOException $e, string $query, array $params = []): void {
       // Handle error based on context (API vs web request)

        $errorData = [
            'error_code' => $e->getCode(),
            'error_message' => $e->getMessage(),
            'sql_state' => $e->errorInfo[0] ?? 'Unknown',
            'driver_error_code' => $e->errorInfo[1] ?? 'Unknown',
            'driver_error_message' => $e->errorInfo[2] ?? 'Unknown',
            'query' => $query,
            'parameters' => $params,
            'table' => $this->table ?? 'Unknown',
            'user_id' => $_SESSION['user_id'] ?? 'Anonymous',
            'request_uri' => $_SERVER['REQUEST_URI'] ?? 'Unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown',
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'Unknown',
            'stack_trace' => $e->getTraceAsString()
        ];

        // Log to database error log
        if (function_exists('tcs_log')) {
            tcs_log($errorData, 'database_errors', true);
        } else {
            // Fallback logging
            error_log('DATABASE ERROR: ' . json_encode($errorData) . "\n", 3, STORE_PAGE_PARSE_TIME_LOG ?? 'php://stderr');
        }

        if (defined('API_RUN') && API_RUN) {
            // For API requests, throw exception to be handled by caller
            throw new DatabaseException(
                'Database query failed: ' . $e->getMessage(),
                $e->getCode(),
                $e,
                $query,
                $params
            );
        } else {
            // For web requests, show user-friendly error
            $this->handleWebError($e, $query, $params);
        }
    }

    /**
     * Handle database errors for web requests
     */
    private function handleWebError(PDOException $e, string $query, array $params = []): void {
        if (defined('DEBUG_MODE') && DEBUG_MODE) {

            // In debug mode, show detailed error
            die('<div style="background: #f8d7da; color: #721c24; padding: 20px; border: 1px solid #f5c6cb; border-radius: 5px; font-family: monospace;">
                <h3>Database Error (Debug Mode)</h3>
                <p><strong>Error:</strong> ' . htmlspecialchars( $e->getMessage()) . '</p>
                <p><strong>Query:</strong> ' . htmlspecialchars($query) . '</p>
                <p><strong>Parameters:</strong> ' . htmlspecialchars(json_encode($params)) . '</p>
                <p><strong>Table:</strong> ' . htmlspecialchars($this->table ?? 'Unknown') . '</p>
                </div>');
        } else {
            // In production, show generic error
            die('<div style="background: #f8d7da; color: #721c24; padding: 20px; border: 1px solid #f5c6cb; border-radius: 5px;">
                <h3>Database Error</h3>
                <p>A database error occurred. Please try again later or contact support if the problem persists.</p>
                <p>Error ID: ' . uniqid() . '</p>
                </div>');
        }
    }

    // Static methods for direct access
    public static function raw(string $value): string {
        return $value;
    }

    public static function query(): database {
        return self::connection();
    }

    public static function schema(): Schema {
        return new Schema();
    }

    /**
     * Execute a raw SQL query - provides compatibility with legacy tep_db_query
     */
    public static function rawQuery(string $query, array $params = []): PDOStatement {
        $instance = self::connection();
        return $instance->executeQuery($query, $params);
    }

    /**
     * Perform database operations (insert/update) - provides compatibility with legacy tep_db_perform
     */
    public static function perform(string $table, array $data, string $action = 'insert', string $parameters = ''): PDOStatement {
        $instance = self::table($table);

        if ($action === 'insert') {
            return $instance->insert($data);
        } elseif ($action === 'update') {
            // Parse the parameters string to build where conditions
            if (!empty($parameters)) {
                // Simple parsing - assumes format like "id = 1" or "id = :id"
                $parts = explode('=', $parameters, 2);
                if (count($parts) === 2) {
                    $column = trim($parts[0]);
                    $value = trim($parts[1]);

                    // Remove quotes if present
                    $value = trim($value, "'\"");

                    $instance->where($column, $value);
                }
            }
            return $instance->update($data);
        }

        throw new DatabaseException("Unsupported action: $action");
    }

    /**
     * Get the last insert ID
     */
    public static function insertId(): string {
        $instance = self::connection();
        return $instance->pdo->lastInsertId();
    }

    /**
     * Get the number of affected rows from a statement
     */
    public static function affectedRows(PDOStatement $stmt): int {
        return $stmt->rowCount();
    }

    /**
     * Get the number of rows from a statement
     */
    public static function numRows(PDOStatement $stmt): int {
        return $stmt->rowCount();
    }

    /**
     * Fetch a single row as associative array
     */
    public static function fetchArray(PDOStatement $stmt): ?array {
        $result = $stmt->fetch(PDO::FETCH_ASSOC);
        return $result === false ? null : $result;
    }

    /**
     * Fetch all rows as associative array
     */
    public static function fetchAll(PDOStatement $stmt): array {
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    }

    /**
     * Fetch all rows as a single column array
     */
    public static function fetchColumn(PDOStatement $stmt): array {
        return $stmt->fetchAll(PDO::FETCH_COLUMN);
    }

    /**
     * Fetch a single row as object
     */
    public static function fetchObject(PDOStatement $stmt): ?object {
        $result = $stmt->fetchObject();
        return $result === false ? null : $result;
    }

    /**
     * Close the database connection
     */
    public static function close(): bool {
        if (self::$instance !== null) {
            self::$instance->pdo = null;
            self::$instance = null;
        }
        return true;
    }

    /**
     * Escape and quote a string for database input
     */
    public static function input(string $string): string {
        $instance = self::connection();
        return substr($instance->pdo->quote($string), 1, -1); // Remove wrapping quotes
    }

    /**
     * Escape string for HTML output
     */
    public static function output(string $string): string {
        return htmlspecialchars($string, ENT_QUOTES, 'UTF-8');
    }

    /**
     * Prepare input data (trim strings, recursively handle arrays)
     */
    public static function prepareInput(mixed $input): mixed {
        if (is_string($input)) {
            return trim($input);
        } elseif (is_array($input)) {
            foreach ($input as $key => $value) {
                $input[$key] = self::prepareInput($value);
            }
        }
        return $input;
    }

    /**
     * Prepare JSON data for database storage
     * Handles both native JSON columns and TEXT fallback
     *
     * @param mixed $data Data to be stored as JSON
     * @return string JSON encoded string ready for database storage
     */
    public static function prepareJsonData(mixed $data): string {
        // Always encode as JSON string for consistent storage
        // This works for both JSON columns and TEXT columns
        $json = json_encode($data, JSON_UNESCAPED_UNICODE);

        if (json_last_error() !== JSON_ERROR_NONE) {
            if (function_exists('tcs_log')) {
                tcs_log([
                    'data' => $data,
                    'json_error' => json_last_error_msg(),
                    'action' => 'encoding_as_empty_object'
                ], 'database_json_encode_errors');
            }
            // Return empty JSON object if encoding fails
            return '{}';
        }

        return $json;
    }

    /**
     * Get MySQL server version information
     */
    public static function getServerInfo(): string {
        $instance = self::connection();
        $stmt = $instance->pdo->query("SELECT VERSION()");
        return $stmt->fetchColumn();
    }

    /**
     * Check if the MySQL server supports JSON columns
     * JSON columns were introduced in MySQL 5.7.8
     */
    public static function supportsJsonColumns(): bool {
        static $supports_json = null;

        if ($supports_json === null) {
            try {
                $instance = self::connection();

                // Get MySQL version
                $stmt = $instance->pdo->query("SELECT VERSION()");
                $version = $stmt->fetchColumn();

                // Extract version number (handle MariaDB and MySQL)
                if (preg_match('/^(\d+)\.(\d+)\.(\d+)/', $version, $matches)) {
                    $major = (int)$matches[1];
                    $minor = (int)$matches[2];
                    $patch = (int)$matches[3];

                    // Check if it's MariaDB (which has different JSON support)
                    if (stripos($version, 'mariadb') !== false) {
                        // MariaDB has JSON as an alias for LONGTEXT, supported since 10.2.7
                        $supports_json = ($major > 10) || ($major == 10 && $minor > 2) ||
                                       ($major == 10 && $minor == 2 && $patch >= 7);
                    } else {
                        // MySQL native JSON support since 5.7.8
                        $supports_json = ($major > 5) || ($major == 5 && $minor > 7) ||
                                       ($major == 5 && $minor == 7 && $patch >= 8);
                    }
                } else {
                    // If we can't parse version, assume no JSON support for safety
                    $supports_json = false;
                }

                // Additional test: try to create a temporary table with JSON column
                if ($supports_json) {
                    try {
                        $test_table = 'json_support_test_' . uniqid();
                        $instance->pdo->exec("CREATE TEMPORARY TABLE `{$test_table}` (`test_json` JSON)");
                        $instance->pdo->exec("DROP TEMPORARY TABLE `{$test_table}`");
                    } catch (PDOException $e) {
                        // If creating JSON column fails, mark as unsupported
                        $supports_json = false;
                    }
                }

                // Log the result for debugging
                if (function_exists('tcs_log')) {
                    tcs_log([
                        'mysql_version' => $version,
                        'supports_json' => $supports_json,
                        'detection_method' => 'version_check_and_test'
                    ], 'database_json_support');
                }

            } catch (Exception $e) {
                // If any error occurs, assume no JSON support
                $supports_json = false;

                if (function_exists('tcs_log')) {
                    tcs_log([
                        'error' => $e->getMessage(),
                        'supports_json' => false,
                        'detection_method' => 'error_fallback'
                    ], 'database_json_support');
                }
            }
        }

        return $supports_json;
    }

    /**
     * Get detailed information about JSON column support
     * Useful for debugging and configuration
     */
    public static function getJsonSupportInfo(): array {
        $instance = self::connection();

        try {
            // Get MySQL version
            $stmt = $instance->pdo->query("SELECT VERSION()");
            $version = $stmt->fetchColumn();

            $supports_json = self::supportsJsonColumns();

            $info = [
                'mysql_version' => $version,
                'supports_json_columns' => $supports_json,
                'is_mariadb' => stripos($version, 'mariadb') !== false,
                'fallback_type' => $supports_json ? 'JSON' : 'TEXT',
                'recommendation' => $supports_json ?
                    'Using native JSON columns for optimal performance' :
                    'Using TEXT columns with JSON encoding - consider upgrading MySQL to 5.7.8+ for native JSON support'
            ];

            return $info;

        } catch (Exception $e) {
            return [
                'error' => $e->getMessage(),
                'supports_json_columns' => false,
                'fallback_type' => 'TEXT',
                'recommendation' => 'Error detecting JSON support - using TEXT fallback'
            ];
        }
    }

    /**
     * Free result resources (PDO handles this automatically, but provided for compatibility)
     */
    public static function freeResult(PDOStatement $stmt): bool {
        $stmt = null;
        return true;
    }

    /**
     * Seek to a specific row in the result set
     * Note: This is inefficient with PDO, but provided for compatibility
     */
    public static function dataSeek(PDOStatement $stmt, int $rowNumber): mixed {
        $results = $stmt->fetchAll(PDO::FETCH_ASSOC);
        return isset($results[$rowNumber]) ? $results[$rowNumber] : false;
    }

    /**
     * Execute query and return all results - provides compatibility with legacy tcs_db_query
     */
    public static function queryAll(string $query, array $params = []): array {
        $stmt = self::rawQuery(trim(preg_replace('/\s+/', ' ', $query)), $params);
        return self::fetchAll($stmt);
    }

    /**
     * Execute query and return single column results - provides compatibility with legacy tcs_db_query_col
     */
    public static function queryColumn(string $query, array $params = []): array {
        $stmt = self::rawQuery(trim(preg_replace('/\s+/', ' ', $query)), $params);
        return self::fetchColumn($stmt);
    }
}

class Schema {
    public static function getConnection(): PDO {
        // Include the database configuration file
        require_once(FS_SYSTEM . "config/db_config.php");

        // Ensure variables are in scope
        global $db_server, $db_username, $db_password, $db_database;

        // If variables are still not set, try to get them directly
        if (empty($db_username) || empty($db_database)) {
            $tcs_database = str_replace('.', '', DOMAIN);
            $db_server = 'localhost';
            $db_username = $tcs_database;
            $db_password = 'S96#1kvYuCGE';
            $db_database = $tcs_database;
        }

        try {
            // Try to connect using TCP/IP
            $dsn = "mysql:host=127.0.0.1;dbname=$db_database;charset=utf8";
            $options = [
                PDO::ATTR_ERRMODE            => PDO::ERRMODE_EXCEPTION,
                PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                PDO::ATTR_EMULATE_PREPARES   => false,
                PDO::ATTR_TIMEOUT            => 5,
            ];

            $pdo = new PDO($dsn, $db_username, $db_password, $options);
            $pdo->exec("set session sql_mode=''");
            return $pdo;
        } catch (PDOException $e) {
            // If TCP/IP fails, try with localhost
            try {
                $dsn = "mysql:host=localhost;dbname=$db_database;charset=utf8";
                $pdo = new PDO($dsn, $db_username, $db_password, $options);
                $pdo->exec("set session sql_mode=''");
                return $pdo;
            } catch (PDOException $e2) {
                die('Connection failed: ' . $e2->getMessage() . ' (Original error: ' . $e->getMessage() . ')' .
                    ' [Username: ' . (empty($db_username) ? 'EMPTY' : 'SET') .
                    ', Database: ' . (empty($db_database) ? 'EMPTY' : $db_database) . ']');
            }
        }
    }

    /**
     * Execute a query with parameters and return the statement
     */
    private static function execute(string $query, array $params = []): PDOStatement {
        try {
            $pdo = self::getConnection();
            $stmt = $pdo->prepare($query);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            // Enhanced error handling for schema operations
            self::handleSchemaError($e, $query, $params);
        }
    }

    /**
     * Handle schema operation errors with enhanced logging
     */
    public static function handleSchemaError(PDOException $e, string $query, array $params = []): void {
        $errorData = [
            'error_code' => $e->getCode(),
            'error_message' => $e->getMessage(),
            'sql_state' => $e->errorInfo[0] ?? 'Unknown',
            'driver_error_code' => $e->errorInfo[1] ?? 'Unknown',
            'driver_error_message' => $e->errorInfo[2] ?? 'Unknown',
            'query' => $query,
            'parameters' => $params,
            'operation_type' => 'SCHEMA',
            'user_id' => $_SESSION['user_id'] ?? 'Anonymous',
            'request_uri' => $_SERVER['REQUEST_URI'] ?? 'Unknown',
            'user_agent' => $_SERVER['HTTP_USER_AGENT'] ?? 'Unknown',
            'ip_address' => $_SERVER['REMOTE_ADDR'] ?? 'Unknown',
            'stack_trace' => $e->getTraceAsString()
        ];

        // Log to database error log
        if (function_exists('tcs_log')) {
            tcs_log($errorData, 'database_schema_errors', true);
        } else {
            // Fallback logging
            error_log('SCHEMA ERROR: ' . json_encode($errorData) . "\n", 3, STORE_PAGE_PARSE_TIME_LOG ?? 'php://stderr');
        }

        // Always throw DatabaseException for schema errors to be caught by calling code
        throw new DatabaseException(
            'Schema operation failed: ' . $e->getMessage(),
            $e->getCode(),
            $e,
            $query,
            $params
        );
    }

    public static function hasTable(string $table): bool {
        // Use INFORMATION_SCHEMA instead of SHOW TABLES LIKE for better prepared statement support
        $query = "SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = ?";
        $stmt = self::execute($query, [$table]);
        return $stmt->fetchColumn() > 0;
    }

    public static function hasColumn(string $table, string $column): bool {
        // Use INFORMATION_SCHEMA instead of SHOW COLUMNS LIKE for better prepared statement support
        $query = "SELECT COUNT(*) FROM INFORMATION_SCHEMA.COLUMNS WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = ? AND COLUMN_NAME = ?";
        $stmt = self::execute($query, [$table, $column]);
        return $stmt->fetchColumn() > 0;
    }

    public static function create(string $table, callable $callback): PDOStatement {
        $blueprint = new Blueprint($table);
        $callback($blueprint);
        return $blueprint->execute();
    }

    public static function table(string $table, callable $callback): PDOStatement {
        $blueprint = new Blueprint($table, true);
        $callback($blueprint);
        return $blueprint->execute();
    }

    public static function drop(string $table): PDOStatement {
        return self::execute("DROP TABLE {$table}");
    }

    public static function dropIfExists(string $table): PDOStatement {
        return self::execute("DROP TABLE IF EXISTS {$table}");
    }
}

class Blueprint {
    private string $table;
    private array $columns = [];
    private bool $isAlter;

    public function __construct(string $table, bool $isAlter = false) {
        $this->table = $table;
        $this->isAlter = $isAlter;
    }

    public function increments(string $name): Blueprint {
        $this->columns[] = "`{$name}` INT UNSIGNED AUTO_INCREMENT PRIMARY KEY";
        return $this;
    }

    public function json(string $name): Blueprint {
        // Check if server supports JSON columns, fallback to TEXT if not
        if (database::supportsJsonColumns()) {
            $this->columns[] = "`{$name}` JSON";

            // Log that we're using native JSON
            if (function_exists('tcs_log')) {
                tcs_log([
                    'table' => $this->table,
                    'column' => $name,
                    'type' => 'JSON',
                    'fallback_used' => false
                ], 'database_json_columns');
            }
        } else {
            // Fallback to TEXT for JSON storage
            $this->columns[] = "`{$name}` TEXT";

            // Log that we're using TEXT fallback
            if (function_exists('tcs_log')) {
                tcs_log([
                    'table' => $this->table,
                    'column' => $name,
                    'type' => 'TEXT (JSON fallback)',
                    'fallback_used' => true,
                    'reason' => 'Server does not support JSON columns'
                ], 'database_json_columns');
            }
        }

        return $this;
    }

    public function string(string $name, int $length = 255): Blueprint {
        $this->columns[] = "`{$name}` VARCHAR({$length})";
        return $this;
    }

    public function integer(string $name): Blueprint {
        $this->columns[] = "`{$name}` INT";
        return $this;
    }

    public function text(string $name): Blueprint {
        $this->columns[] = "`{$name}` TEXT";
        return $this;
    }

    public function timestamp(string $name): Blueprint {
        $this->columns[] = "`{$name}` TIMESTAMP";
        return $this;
    }

    public function timestamps(): Blueprint {
        $this->columns[] = "`created_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP";
        $this->columns[] = "`updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP";
        return $this;
    }

    public function decimal(string $name, int $precision = 8, int $scale = 2): Blueprint {
        $this->columns[] = "`{$name}` DECIMAL({$precision},{$scale})";
        return $this;
    }

    public function date(string $name): Blueprint {
        $this->columns[] = "`{$name}` DATE";
        return $this;
    }

    public function boolean(string $name): Blueprint {
        $this->columns[] = "`{$name}` BOOLEAN";
        return $this;
    }

    public function nullable(): Blueprint {
        // Modify the last added column to be nullable
        if (!empty($this->columns)) {
            $lastIndex = count($this->columns) - 1;
            $this->columns[$lastIndex] .= " NULL";
        }
        return $this;
    }

    public function default($value): Blueprint {
        // Modify the last added column to have a default value
        if (!empty($this->columns)) {
            $lastIndex = count($this->columns) - 1;
            if (is_string($value)) {
                $this->columns[$lastIndex] .= " DEFAULT '{$value}'";
            } else {
                $this->columns[$lastIndex] .= " DEFAULT {$value}";
            }
        }
        return $this;
    }

    public function execute(): PDOStatement {
        if ($this->isAlter) {
            $query = "ALTER TABLE {$this->table} ADD " . implode(", ADD ", $this->columns);
        } else {
            $query = "CREATE TABLE {$this->table} (" . implode(", ", $this->columns) . ")";
        }

        // Log the query for debugging
        $this->logSchemaQuery($query);

        try {
            // Execute the query using Schema's connection
            $pdo = Schema::getConnection();
            $stmt = $pdo->prepare($query);
            $stmt->execute();
            return $stmt;
        } catch (PDOException $e) {
            // Enhanced error handling for schema operations
            Schema::handleSchemaError($e, $query, []);
        }
    }

    /**
     * Log schema queries for debugging
     */
    private function logSchemaQuery(string $query): void {
        $logData = [
            'query' => $query,
            'table' => $this->table,
            'type' => $this->isAlter ? 'ALTER' : 'CREATE',
            'timestamp' => date('Y-m-d H:i:s')
        ];

        if (function_exists('tcs_log')) {
            tcs_log($logData, 'database_schema');
        } else {
            // Fallback logging
            error_log('SCHEMA QUERY: ' . $query . "\n", 3, STORE_PAGE_PARSE_TIME_LOG ?? 'php://stderr');
        }
    }


}
