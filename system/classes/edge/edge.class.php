<?php
namespace edge;
use const icons\ICONS;
class edge
{
    private static $renderDepth = 0;
    static int $id_count = 0;
    protected static array $base_patterns = [
        // Regex patterns
        'props_declaration' => '/@props\s*\(\s*\[(.*?)\]\s*\)/s',
        'variable' => '/(?<!\@)(?:\{\{|\{!!)\s*(.*?)\s*(?:!!\}|\}\})/',

        'edge_tag_open' => '/<x-([\w-]+|{{ [^}]* }})(.*?)(?:(\/>)|(?<!-)(?<!=)>)/s',
        'edge_tag_close' => '/<\/x-([\w-]+|{{ [^}]* }})>/s',
        'edge_tag_content' => '/<x-([a-zA-Z_\-]*)[^>]*>.+?<\/x-\1>/s',
        'component_tag_open' => '/@component\(([^)]+)\)/s',
        'component_tag_content' => '/@component\(.+?\)(.+?)@endcomponent/s',
        'component_tag_close' => '/@endcomponent/',
        'attribute' => '/([\w\-:@_]+)=(?|(?:([\'\"])([^{}]*?)\2)|([^{} ]*?)[ >]|([\'\"])([^{} ]*?(?>{{ )[^}]*(?: }})[^{} ]*?)\2)/s',
        'comments' => '/(<!|{{)--.*?--(>|}})/s',
        'foreach_loop' => '/@foreach\s*\((.+?)( as [^)]+)\)/',
        'if_statement' => '/@if\s*\((.*)\)/',
        'icon' => '/@icon([^(]*)\((([^)]*))\)/',
        'elseif_statement' => '/@elseif\s*\((.*)\)/',
        'php_include' => '/@(include|require)(_once)?((?:[\s\(])*([a-zA-Z$_-]*)[\)]?)/',
        'util_function' => '/@(print_rr|log)\s*\((.*?)\)/',
        'view' => '/@view\s*([a-zA-Z$_-]*)/',
        'db_query' => '/@db\s*\(\s*[\'"]([^\'"]+)[\'"]\s*(?:,\s*(.+?))?\s*\)/',
        'db_table' => '/@table\s*\(\s*[\'"]([^\'"]+)[\'"]\s*\)/',
        'db_get' => '/@get\s*\(\s*(.+?)\s*\)/',
      //  'iconsConst' => '/[^\\](ICONS\[[\'|\"][^\]]+?[\'|\"]\])/',

        // String patterns
        'php_tag_open' => '@php',
        'php_tag_close' => '@endphp',
        'else' => '@else',
        'endif' => '@endif',
        'break' => '@break',
        'endforeach' => '@endforeach',
        'tag_strip' => ['\<\?php ', '<?= ', ' ?>'],
        'php_consecutive_conditionals_tags' => '/([:}])\s*\?>(\s*)<\?php/',
        'php_consecutive_conditionals_echo_tags' => '/([:}])\?>(\s*)<\?=/',
        'php_consecutive_tags' => '/\?>(\s*)<\?php/',
        'php_consecutive_echo_tags' => '/\?>(\s*)<\?=/',
        'html_minify' => '/\>\s*\</s',
        'html_minify_newLines' => '/\s*\R\s*/',
        //section tags

        'tag_content' => '/(?>\{\{\=\s*tag_content\s*(?>\=\}\}))/',
    ];

    //1
    protected static $secondary_patterns = [];
    // 2
    protected static $patterns = [];
    protected static array $replacements = [
        'variable' => '<?= $1 ?>',
        'variable_notag' => '$1$2',
        'foreach_loop' => '<?php if (is_countable($1)) { $loop = new loop(count($1),$loop->depth+1,$loop); foreach($1$2): ?>',
        'foreach_end' => '<?php $loop->update(); endforeach; $loop = $loop->parent; } ?>',
        'if_end' => '<?php endif ?>',
        'else' => '<?php else: ?>',
        'break' => '<?php break; ?>',
        'component_tag' => '<?php edge::render($1); ?>',
        'icon' => '<?= icon$1($2) ?>',
        'blade_comments' => '',
        'php_tag_open' => '<?php',
        'php_tag_close' => '?>',
        'php_include' => '<?php $1$2$3 ?>',
        'view' => '<?php router::get_route($1) ?>',
        'db_query' => '<?php $db_result = database::query()->raw("$1"); ?>',
        'db_table' => '<?php $db_table = database::table("$1"); ?>',
        'db_get' => '<?php $db_data = $1->get(); ?>',
        'html_minify' => '><',
        'html_minify_newLines' => ' ',
        'php_consecutive_tags' => ';$1',
        'php_consecutive_echo_tags' => ';$1 echo',
        'php_consecutive_conditional_tags' => '$1$2',
        'php_consecutive_conditional_echo_tags' => '$1$2 echo',
        'util_function' => '<?php $1($2); ?>',
        //section tags
        //  'iconsConst' =>  '\\icons\\$1',
        'tag_content' => '<?php } if($tag_content == 0 || $tag_content == 2 ){ ?>',
    ];
    protected static array $dependencies = [
        '$loop' => [
            'key' => 'edge_loop',
            'namespace' => 'edge\\loop',
            'code' => '$loop = new Loop(0)'
        ],
        '$pagination' => [
            'key' => 'edge_pagination',
            'namespace' => 'edge\\pagination',
            'code' => '$pagination = new pagination($item_count,$first_item,$current_page_num,$items_per_page)'
        ],
        'router::' => [
            'key' => 'router',
            'namespace' => 'system\\router'
        ],
        'autodesk_api::' => [
            'key' => 'autodesk_api',
            'namespace' => 'autodesk_api\\autodesk_api',
        ],
        'data_table::' => [
            'key' => 'data_table',
            'namespace' => 'data_table\\data_table',
        ],
        'users::' => [
            'key' => 'users',
            'namespace' => 'system\\users',
        ],
        'database::' => [
            'key' => 'database',
            'namespace' => 'system\\database',
        ],
        'data_importer::' => [
            'key' => 'data_importer',
            'namespace' => 'system\\data_importer',
        ],
        'data_table_generator::' => [
            'key' => 'data_table_generator',
            'namespace' => 'system\\data_table_generator',
        ],
        'table_config_manager::' => [
            'key' => 'table_config_manager',
            'namespace' => 'system\\table_config_manager',
        ],
        'hilt::' => [
            'key' => 'hilt',
            'namespace' => 'system\\hilt',
        ],
        'ICONS' => [
            'key' => 'icons',
            'namespace' => 'const icons\\ICONS',
        ],
        'icon(' => [
            'key' => 'icons',
            'namespace' => 'function icons\\icon',
        ],
        'icon_micro(' => [
            'key' => 'icons',
            'namespace' => 'function icons\\icon_micro'
        ]
    ];

    /**
     * @param string $edge_name
     * @param array $data
     * @return false|string
     */
    public static function render(string $edge_name, array|null $data = [], int|null $tag_content = 0): string {
       // tcs_log("Starting: $edge_name'", 'edge');
        //if (DEBUG_MODE) print_rr($data, "data for $edge_name");
        self::$renderDepth++;
        // error_log("Debug: Edge::render depth {$self::renderDepth} for {$edge_name}");
        if (self::$renderDepth > 10) {
            error_log("Warning: Maximum render depth exceeded for {$edge_name}");
            self::$renderDepth--;
            return "Error: Maximum template depth exceeded";
        }

        $system_edge_path = 'system/components/edges/' . $edge_name . '.edge.php';
        $edge_path = 'resources/components/edges/' . $edge_name . '.edge.php';
        $blade_path = 'resources/components/edges/' . $edge_name . '.blade.php';
        $compiled_path = FS_TEMP . '/' . $edge_name . '.edge.php';

        if (file_exists($edge_path)) {
            $file_path = $edge_path;
        } elseif (file_exists($system_edge_path))  {
            $file_path = $system_edge_path;
        }elseif (file_exists($blade_path)) {
            $file_path = $blade_path;
        } else {
            return die("Edge file not found: {$edge_path}");
        }
        $data['tag_content'] = $tag_content;
        $data['edge_manifest']['name'] = $edge_name;
        $data['edge_manifest']['file_path'] = $file_path;
        // Check if cached version exists

        if (!file_exists($compiled_path) || filemtime($file_path) > filemtime($compiled_path) || filemtime(__FILE__) > filemtime($compiled_path)) {
            tcs_log("compiling_path: '$file_path' to '$compiled_path'");
            $uncompiled = file_get_contents($file_path);
            $compiled = self::compile($uncompiled, $edge_name,$data['edge_manifest']);
            file_put_contents($compiled_path, $compiled);
        }
        self::$renderDepth--;
       // print_rr(input:tcs_log("starting rendering: $compiled_path'", 'edge'),full:true);

        return self::phprender($data, $compiled_path);
    }

    public static function phprender($edge_data, $compiled_path): false|string {
        ob_start();
        extract($edge_data);
     //   print_rr(tcs_log("Including: $compiled_path'", 'edge'));
        include($compiled_path);
     //   print_rr(tcs_log("Complete $compiled_path", 'edge'));
        return ob_get_clean();
    }

    /**
     * Renders a view file using Edge template syntax
     *
     * @param string $view_path Path to the view file
     * @param array $data Data to pass to the view
     * @return string Rendered view content
     */
    public static function renderView(string $view_path, array $data = []): string {
        // Generate a unique name for the compiled view
        $view_hash = md5($view_path);
        $compiled_dir = 'resources/views/.compiled/';

        // Create the compiled directory if it doesn't exist
        if (!file_exists($compiled_dir)) {
            mkdir($compiled_dir, 0755, true);
        }

        $compiled_path = $compiled_dir . $view_hash . '.comp.php';

        // Check if we need to compile the view
        if (!file_exists($compiled_path) || filemtime($view_path) > filemtime($compiled_path) || filemtime(__FILE__) > filemtime($compiled_path)) {
            $uncompiled = file_get_contents($view_path);
            $compiled = self::compile($uncompiled, 'view_' . $view_hash);
            file_put_contents($compiled_path, $compiled);
        }
        // Render the compiled view
        return self::phprender($data, $compiled_path);
    }

    public static function id_count(): int {
        if (!isset($_SESSION['id_count'])) $_SESSION['id_count'] = 0;
        return ++$_SESSION['id_count'];
    }

    public static function compile($source, $edge_name,$manifest = []): string    {
        self::$secondary_patterns = [
            'bound_variable' => '/(?<=[\'"])' . trim(self::$base_patterns['variable'],'/') . '/',
            'bound_sub_variable' => '/(?<![\'"])' . trim(self::$base_patterns['variable'],'/') . '/'
        ];
        self::$patterns = array_merge(self::$base_patterns,self::$secondary_patterns);
        $pipeline = [
            'handle_tag_sections',
            'insert_view',
            'remove_comments',
            'compile_edge_tags',
            'compile_variables',
            'compile_conditional_statements',
            'compile_statements',
            'compile_loops',
            'compile_php_tags',
            'compile_database_queries',
            'compile_icons',
            'compile_functions',
            'compile_finalize'
        ];

        return array_reduce($pipeline, function ($carry, $method) use ($edge_name,$manifest) {
            return self::$method($carry, $edge_name,$manifest);
        }, $source);
    }
    protected static function compile_functions($source) {
        return preg_replace(self::$patterns['util_function'], self::$replacements['util_function'], $source);
    }
    protected static function debigulateHTML($source) {
      //  $source = preg_replace(self::$patterns['php_consecutive_conditional_echo_tags'], self::$replacements['php_consecutive_conditional_echo_tags'],
    //        preg_replace(self::$patterns['php_consecutive_conditional_tags'], self::$replacements['php_consecutive_conditional_tags'], $source));
//
   //     return preg_replace(self::$patterns['php_consecutive_echo_tags'], self::$replacements['php_consecutive_echo_tags'],
   //         preg_replace(self::$patterns['php_consecutive_tags'], self::$replacements['php_consecutive_tags'], $source)        );
    return preg_replace(self::$patterns['html_minify_newLines'], self::$replacements['html_minify_newLines'],
          preg_replace(self::$patterns['html_minify'], self::$replacements['html_minify'], $source)
      );
    }

    protected static function insert_functionality(string $source, $edge_name): array {

        $edge_name = str_replace('-', '_', $edge_name);
        $namespace_inject = $include_inject = $function_inject = $code_inject = $class_inject = [];
        // Define an associative array of dependencies to check for

        // Process each dependency
        foreach (self::$dependencies as $search => $config) {
            $found = strpos($source, $search);
            if ($found !== false) {
                if (isset($config['namespace'])) {
                    $key = $config['key'] . crc32($config['namespace']);
                    $namespace_inject[$key] = $config['namespace'];
                }
                if (isset($config['class'])) {
                    $class_inject[$config['class']] = $config['class'];
                }
                if (isset($config['code'])) {
                    $key = $config['key'] . crc32($config['code']);
                    $code_inject[$key] = $config['code'];
                }
            }
        }

        $namespace_inject_string = "namespace edgeTemplate\\{$edge_name};use edge\\Edge;";
        if (!empty($namespace_inject)) {
            $namespace_inject_string .= 'use ' . implode(';use ', $namespace_inject) . ';';
        }

        $include_inject_string = '';
        foreach($include_inject as $include) {
            $include_inject_string .= "require_once(FS_CLASSES . '/{$include}.class.php');";
        }
        $function_inject_string = '';
        foreach($function_inject as $include) {
            $function_inject_string .= "require_once(FS_FUNCTIONS . '/{$include}.fn.php');";
        }
        $class_inject_string = '';
        foreach($class_inject as $include) {
            $class_inject_string .= "require_once(FS_CLASSES . '/{$include}.class.php');";
        }
        $code_inject_string = implode(';',$code_inject) . ';';

        $inject = [
            'namespace' => $namespace_inject_string,
            'class' => $class_inject_string,
            'include' => $include_inject_string,
            'function' => $function_inject_string,
            'code' => $code_inject_string,
            'tag_content_start' => 'print_rr($tag_content,"tag_content_start"); if (!isset($tag_content) || $tag_content == 0 || $tag_content == 1 ){',
            'tag_content_end' => '}'
        ];
        print_rr($inject, "inject for $edge_name");
        return $inject;
    }

    protected static function insert_view(string $source): string  {

        $source = preg_replace(self::$patterns['view'], self::$replacements['view'], $source);
        return $source;
    }

    protected static function compile_finalize($source, $edge_name, $manifest): string    {
        $inject = self::insert_functionality($source, $edge_name);

        if (preg_match(self::$patterns['props_declaration'], $source, $m)) {
            $props_array_string = trim($m[1]);
            $props_array_string = preg_replace('/\/\/.*/', '', $props_array_string);
            $props_array_string = "['edge_manifest' => " . var_export($manifest, true) . "," . $props_array_string . PHP_EOL . "]";
            $source = str_replace($m[0], "<?php extract(Edge::pp($props_array_string,\$edge_data));{$inject['class']}{$inject['include']}{$inject['code']}{$inject['function']}{$inject['tag_content_start']}?>", $source);
        } else {
            $source = "<?php extract(['edge_manifest' => " . var_export($manifest, true) . "]);" . $inject['class']  .  $inject['include']  .  $inject['code']  .  $inject['function'] . $inject['tag_content_start'] . ' ?>' . $source;
        }
        return '<?php ' .$inject['namespace'] . '?>' . $source .  '<?php ' . $inject['tag_content_end'] . ' ?>';
    }

    protected static function remove_comments($source)
    {
        return preg_replace(self::$patterns['comments'], '', $source);
    }

    protected static function compile_variables($source)
    {
        return preg_replace(self::$patterns['variable'], self::$replacements['variable'], $source);
    }

    protected static function compile_conditional_statements($source): string
    {
        return preg_replace_callback(self::$patterns['elseif_statement'], function ($matches) {
            $startPos = strpos($matches[0], '(');
            $condition = self::extractCondition($matches[0], $startPos);
            return "<?php elseif{$condition}: ?>";
        }, preg_replace_callback(self::$patterns['if_statement'], function ($matches) {
            $startPos = strpos($matches[0], '(');
            $condition = self::extractCondition($matches[0], $startPos);
            return "<?php if{$condition}: ?>";
        }, $source));
    }

    protected static function compile_statements($source): string
    {
        $source = str_replace(self::$patterns['else'], self::$replacements['else'], $source);
        $source = str_replace(self::$patterns['endif'], self::$replacements['if_end'], $source);
        $source = str_replace(self::$patterns['break'], self::$replacements['break'], $source);
        return $source;
    }

    protected static function compile_loops($source): string
    {
        $source = preg_replace(self::$patterns['foreach_loop'], self::$replacements['foreach_loop'], $source);
        $source = str_replace(self::$patterns['endforeach'], self::$replacements['foreach_end'], $source);
        return $source;
    }

    protected static function compile_php_tags($source): string
    {
        $source = preg_replace(self::$patterns['php_include'], self::$replacements['php_include'], $source);
        $source = str_replace(self::$patterns['php_tag_open'], self::$replacements['php_tag_open'], $source);
        $source = str_replace(self::$patterns['php_tag_close'], self::$replacements['php_tag_close'], $source);
        return $source;
    }

    /**
     * Compile database query directives
     *
     * @param string $source Template source
     * @return string Compiled source
     */
    protected static function compile_database_queries($source): string
    {
        $source = preg_replace(self::$patterns['db_query'], self::$replacements['db_query'], $source);
        $source = preg_replace(self::$patterns['db_table'], self::$replacements['db_table'], $source);
        $source = preg_replace(self::$patterns['db_get'], self::$replacements['db_get'], $source);
        return $source;
    }

protected
static function compile_icons( $source ): string {
        return preg_replace(self::$patterns['icon'], self::$replacements['icon'], $source);
}

protected static function compile_edge_tags($source,$edge_name) {
    $section_string = '';
    $simpleTags = true;
    if (preg_match(self::$patterns['edge_tag_content'], $source) || preg_match(self::$patterns['component_tag_content'], $source)) {
        $section_string = ', 1';
        $simpleTags = false;
        print_rr('detected edge_tag_content in ' . $edge_name);
    } else {
        print_rr('no edge_tag_content detected in ' . $edge_name);
    }

    $source = preg_replace_callback(
        self::$patterns['edge_tag_open'],
        function ($matches) use ($section_string) {
            $componentName = $matches[1];
            if (str_starts_with($componentName, '{{')) {
                $componentName = preg_replace(self::$patterns['variable'], self::$replacements['variable_notag'], $componentName);
            } else {
                $componentName = "'{$componentName}'";
            }
            $attributes = $matches[2];

            $props = self::parseAttributes($attributes);

            print_rr($props, 'compile_component_tags_props');
            $propsString = is_array($props) ? implode(', ', $props): $props;
            $section_string = $matches[3] == '/>' ? ', 0' : $section_string;
            return "<?= Edge::render({$componentName}, [" . $propsString . "]{$section_string}) ?>";
        },
        $source);


    $source = preg_replace_callback(
        self::$patterns['component_tag_open'],
        function ($matches) use ($section_string) {
            $attributes = $matches[1];
            return "<?= Edge::render({$attributes}{$section_string}) ?>";
        },
        $source);

    if ($simpleTags) return preg_replace(self::$patterns['edge_tag_close'], '', $source);
    $source = preg_replace_callback(
        self::$patterns['edge_tag_close'],
        function ($matches) {
            $componentName = $matches[1];
            if (str_starts_with($componentName, '{{')) {
                $componentName = preg_replace(self::$patterns['variable'], self::$replacements['variable_notag'], $componentName);
            } else {
                $componentName = "'{$componentName}'";
            }
            return "<?= Edge::render({$componentName},null,2) ?>";
        },
        $source
    );
    return preg_replace_callback(
        self::$patterns['component_tag_close'],
        function ($matches) {
            $componentName = $matches[1];
            return "<?= Edge::render('{$componentName}',null,2) ?>";
        },
        $source
    );
}

protected static function handle_tag_sections($source){
    return preg_replace(self::$patterns['tag_content'], self::$replacements['tag_content'], $source);
}


public static function parse_tables($schema, $fields = []) {
    foreach ($schema as $key => $value) {
        if (is_array($value)) {
            if (isset($value['content'])) {
                $fields = self::parse_tables($value['content'], $fields);
            } elseif (isset($value['value'])) {
                $fields[$key] = $key;
            }
        }
    }
    return $fields;
}

public static function parse_layout($layout, $data) {
    foreach ($layout as $key => $value) {
        if (is_array($value)) {
            if (isset($value['content'])) {
                $layout[$key]['content'] = self::parse_layout($value['content'], $data);
            } elseif (isset($value['value'])) {
                $layout[$key]['value'] = $data[$key] ?? '';
            }
        }
    }
    return $layout;
}

protected static function parseAttributes($attributeString){
    $props = [];
    preg_match_all(self::$patterns['attribute'], $attributeString, $attrMatches, PREG_SET_ORDER);

    foreach ($attrMatches as $attr) {
        $key = $attr[1];
        $rawValue = $attr[3];
        if ($key == 'x-edge-data') return $rawValue;
        // Handle bound values (e.g., :options="...")
        if (str_starts_with($key, ':') && !str_starts_with($key,':::')) {
            $key = substr($key, 1);
            // Check if the value contains template variables
            if (preg_match(self::$patterns['bound_variable'], $rawValue)) {
                $value = preg_replace(self::$patterns['bound_variable'], '$1', $rawValue);
                $props[] = "'$key' => $value";
            } elseif(preg_match(self::$patterns['bound_sub_variable'], $rawValue)) {
                    $value =  preg_replace(self::$patterns['bound_sub_variable'], '" . $1 . "', $rawValue);
                    $props[] = "'$key' => \"$value\"";
            } else {
                // If no template variables, treat as direct PHP expression
                $value = $rawValue;
                $props[] = "'$key' => $value";
            }
        } else {
            if (str_starts_with($key, ':::'))  $key = substr($key, 2);
            // Regular attributes - preserve template variables
            if (preg_match(self::$patterns['variable'], $rawValue)) {
                $value = preg_replace(self::$patterns['variable'], '" . $1 . "', $rawValue);
                $props[] = "\"$key\" => \"$value\"";
            } else {
                // Regular string value
                $rawValue = preg_replace('/([$"\'])/', '\\\$1', $rawValue);
                $props[] = "\"$key\" => \"$rawValue\"";
            }
        }
    }
        return $props;
    }


public  static function extractCondition($content, $start): string {
    $stack = [];
    $result = '';
    $inside = false;
    for ($i = $start; $i < strlen($content); $i++) {
        $char = $content[$i];
        if ($char === '(') {
            if ($inside) {
                $stack[] = $char;
            } else {
                $inside = true;
            }
            $result .= $char;
        } elseif ($char === ')') {
            if (!empty($stack)) {
                array_pop($stack);
                $result .= $char;
            } elseif ($inside) {
                $result .= $char;
                break;
            }
        } elseif ($inside) {
            $result .= $char;
        }
    }
    return $result;
}


public
static function pp($defaultProps, $props = [], $debug = false)
{
    return process_props($defaultProps, $props, $debug);
}

public
static function process_props($defaultProps, $props = [], $debug = false)
{
    $extra_attributes = '';
    if ($debug) print_rr($defaultProps, "defaultProps");
    foreach ($props as $key => $prop) {
        if ($debug) print_rr("is $key in defaultProps", "default props");
//        if (($key == 'icon' || $key == 'edge-icon') && key_exists('edge-icon', $defaultProps)) {
//            $props['icon'] = ICONS[$prop];
//            continue;
//        }
        if (isset($defaultProps[$key])) continue;
        if (is_array($prop)) {
            $extra_attributes .= " {$key}='" . json_encode($prop) . "'";
        } else {
            if (is_int($key) || is_float($key) || is_bool($key)) {
                $extra_attributes .= " {$key}=$prop";
            } else {
                $extra_attributes .= " {$key}='{$prop}'";
            }
        }
    }
    $props = array_merge($defaultProps, $props);
    $props['extra_attributes'] = $extra_attributes;
    if ($debug) print_rr($props, "template props");
    //   print_rr($props, 'proppage');
    return $props;
}


/**
 * Merges multiple strings of Tailwind CSS classes, filters duplicates,
 * and overwrites conflicting classes, with the last string taking precedence.
 *
 * @param string ...$classStrings Multiple strings of Tailwind CSS classes.
 *
 * @return string A single string containing the merged and processed
 *                Tailwind CSS classes.
 */
public
static function merge_tailwind_classes(string ...$classStrings): string
{
    $classes = [];

    foreach ($classStrings as $classString) {
        $classArray = preg_split('/\s+/', trim($classString));

        foreach ($classArray as $class) {
            if (empty($class)) {
                continue; // Skip empty classes
            }

            // Extract the base class name (e.g., 'bg' from 'bg-red-500')
            preg_match('/^([a-zA-Z]+(?:-[a-zA-Z]+)*)/', $class, $matches);
            $baseClass = $matches[1] ?? $class; // Use the whole class if no match

            $classes[$baseClass] = $class; // Overwrite if the base class exists
        }
    }

    return implode(' ', array_values($classes));
}

public
static function column_filter($columns): array
{
    print_rr($columns, 'subs_api_data_table_filter');
    $cols = $criteria = [];
    foreach ($columns as $column => $value) {
        if (empty($value)) continue;
        print_rr($column, ' val: ' . $value);
        $col_parts = explode("_", $column, 2);
        $table = $col_parts[0];
        $column_name = $col_parts[1];
        $cols["{$table}.{$column_name}"] = ['=', $value];
    }
    if (count($cols) > 0) $criteria["where"] = $cols;
    return $criteria;
}


}