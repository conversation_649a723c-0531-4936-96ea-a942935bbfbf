<?php
namespace system;

use system\database;
use system\data_importer;
use edge\Edge;

/**
 * Hilt Class - Template processing system for database-driven views
 * 
 * This class handles the creation and management of database-driven templates
 * that use the .hilt.php extension. It provides functionality for:
 * - Creating database tables with naming convention autobooks_{route_key}_data
 * - Importing CSV data using the data_importer class
 * - Processing hilt templates for Edge/PHP based views
 */
class hilt {
    private static string $log_target = 'hilt';
    
    /**
     * Process a hilt template file based on template type
     *
     * @param string $template_file Path to the hilt template file
     * @param array $template_data Template data from form
     * @param array $files Uploaded files
     * @param string $key Route key
     * @param string $template_type Type of template (database, html, file_upload, default)
     * @return string Generated view content
     */
    public static function process_template(string $template_file, array $template_data, array $files, string $key, string $template_type = 'database'): string {
        try {
            tcs_log("Processing hilt template: {$template_type} for key: {$key}", self::$log_target);

            switch ($template_type) {
                case 'database':
                case 'csv':
                    return self::process_database_template($template_file, $template_data, $files, $key);
                case 'html':
                case 'custom_html':
                    return self::process_html_template($template_file, $template_data, $files, $key);

                case 'file_upload':
                    return self::process_file_upload_template($template_file, $template_data, $files, $key);

                case 'default':
                case 'none':
                default:
                    return self::process_default_template($template_file, $template_data, $files, $key);
            }

        } catch (\Exception $e) {
            tcs_log("Error processing hilt template: " . $e->getMessage(), self::$log_target);
            return self::generate_error_view("Template processing failed: " . $e->getMessage());
        }
    }

    /**
     * Process database-driven hilt template
     *
     * @param string $template_file Path to the template file
     * @param array $template_data Template data from form
     * @param array $files Uploaded files
     * @param string $key Route key
     * @return string Generated view content
     */
    private static function process_database_template(string $template_file, array $template_data, array $files, string $key): string {
        // Create database table for this route
        $table_name = "autobooks_{$key}_data";

        if (!self::ensure_table_exists($table_name)) {
            tcs_log("Failed to create table: {$table_name}", self::$log_target);
            return self::generate_error_view("Failed to create database table");
        }

        // Import CSV data if provided
        if (!empty($files) || !empty($template_data['csv_data'])) {
            $import_result = self::import_csv_data($table_name, $template_data, $files);
            if (isset($import_result['error'])) {
                tcs_log("CSV import failed: " . $import_result['error'], self::$log_target);
                return self::generate_error_view("Failed to import CSV data: " . $import_result['error']);
            }
        }

        // Get template content and process it
        $template_content = file_get_contents($template_file);

        // Replace template placeholders
        $template_content = str_replace('{{ $table_name }}', $table_name, $template_content);
        $template_content = str_replace('{{ $route_key }}', $key, $template_content);

        tcs_log("Successfully processed database hilt template for key: {$key}", self::$log_target);
        return $template_content;
    }
    
    /**
     * Ensure database table exists, create if necessary
     *
     * @param string $table_name Table name
     * @return bool Success status
     */
    public static function ensure_table_exists(string $table_name): bool {
        try {
            // Check if table exists using the Schema class
            $schema = database::schema();
            if ($schema::hasTable($table_name)) {
                tcs_log("Table {$table_name} already exists", self::$log_target);
                return true;
            }

            // Create table with flexible structure for CSV data
            $schema::create($table_name, function($table) {
                $table->increments('id');
                $table->json('data_json'); // Store row data as JSON for flexibility
                $table->string('data_hash', 64); // For duplicate detection
                $table->timestamps();
            });

            tcs_log("Created table: {$table_name}", self::$log_target);
            return true;

        } catch (\system\DatabaseException $e) {
            // Enhanced logging for database-specific errors
            tcs_log([
                'action' => 'table_creation_failed',
                'table_name' => $table_name,
                'error_message' => $e->getMessage(),
                'query' => $e->getQuery(),
                'parameters' => $e->getParams(),
                'detailed_message' => $e->getDetailedMessage()
            ], self::$log_target, true);
            return false;
        } catch (\Exception $e) {
            // Fallback for non-database errors
            tcs_log("Error creating table {$table_name}: " . $e->getMessage(), self::$log_target);
            return false;
        }
    }

    /**
     * Import CSV data into the database table
     *
     * @param string $table_name Database table name
     * @param array $template_data Template data from form
     * @param array $files Uploaded files
     * @return array Import result
     */
    public static function import_csv_data(string $table_name, array $template_data, array $files): array {
        try {
            // Check if enhanced schema detection should be used
            $use_enhanced_import = ($template_data['auto_schema'] ?? false) ||
                                   ($template_data['enhanced_import'] ?? false) ||
                                   strpos($table_name, 'enhanced') !== false;

            // Handle file upload
            if (isset($files['csv_file']) && $files['csv_file']['error'] === UPLOAD_ERR_OK) {
                $csv_file_path = $files['csv_file']['tmp_name'];
                tcs_log("Processing uploaded CSV file: {$csv_file_path}", self::$log_target);

                if ($use_enhanced_import) {
                    tcs_log("Using enhanced import with auto-schema detection", self::$log_target);
                    return data_importer::import_csv_with_auto_schema($csv_file_path, $table_name, true, false);
                } else {
                    return data_importer::import_csv_to_hilt_table($table_name, $csv_file_path, true, false);
                }
            }
            // Handle textarea input
            elseif (!empty($template_data['csv_data'])) {
                $csv_data = $template_data['csv_data'];
                tcs_log("Processing CSV data from textarea", self::$log_target);

                if ($use_enhanced_import) {
                    // Create temporary file for enhanced import
                    $temp_file = tempnam(sys_get_temp_dir(), 'hilt_enhanced_');
                    file_put_contents($temp_file, $csv_data);
                    $result = data_importer::import_csv_with_auto_schema($temp_file, $table_name, true, false);
                    unlink($temp_file);
                    return $result;
                } else {
                    return data_importer::import_csv_to_hilt_table($table_name, $csv_data, false, false);
                }
            } else {
                return ['error' => 'No CSV data provided'];
            }

        } catch (\Exception $e) {
            tcs_log("Error importing CSV data: " . $e->getMessage(), self::$log_target);
            return ['error' => "CSV import failed: " . $e->getMessage()];
        }
    }

    /**
     * Create mapping configuration for data_importer
     *
     * @param string $table_name Database table name
     * @param array $headers CSV headers
     * @return array Mapping configuration
     */
    private static function create_csv_mapping(string $table_name, array $headers): array {
        $mapping = [
            'main' => [
                'db_table' => $table_name,
                'db_key' => 'data_hash',
                'columns' => [
                    'data_json' => function($row) use ($headers) {
                        $data = [];
                        foreach ($headers as $index => $header) {
                            $data[$header] = $row[$index] ?? '';
                        }
                        return json_encode($data);
                    },
                    'data_hash' => function($row) {
                        return md5(serialize($row));
                    }
                ]
            ]
        ];

        return $mapping;
    }
    
    /**
     * Generate error view content
     *
     * @param string $error_message Error message to display
     * @return string Error view content
     */
    private static function generate_error_view(string $error_message): string {
        return "<?php
// Error in hilt template processing
echo '<div class=\"p-4 bg-red-100 border border-red-400 text-red-700 rounded\">';
echo '<h2 class=\"text-lg font-bold mb-2\">Template Error</h2>';
echo '<p>" . htmlspecialchars($error_message) . "</p>';
echo '</div>';
?>";
    }

    /**
     * Update CSV data in existing table
     *
     * @param string $table_name Database table name
     * @param array $template_data Template data
     * @param array $files Uploaded files
     * @param bool $replace_all Whether to replace all data or merge
     * @return array Update result
     */
    public static function update_csv_data(string $table_name, array $template_data, array $files, bool $replace_all = false): array {
        try {
            // Handle file upload
            if (isset($files['csv_file']) && $files['csv_file']['error'] === UPLOAD_ERR_OK) {
                $csv_file_path = $files['csv_file']['tmp_name'];
                tcs_log("Processing uploaded CSV file for update: {$csv_file_path}", self::$log_target);
                return data_importer::import_csv_to_hilt_table($table_name, $csv_file_path, true, $replace_all);
            }
            // Handle textarea input
            elseif (!empty($template_data['csv_data'])) {
                $csv_data = $template_data['csv_data'];
                tcs_log("Processing CSV data from textarea for update", self::$log_target);
                return data_importer::import_csv_to_hilt_table($table_name, $csv_data, false, $replace_all);
            } else {
                return ['error' => 'No CSV data provided'];
            }

        } catch (\Exception $e) {
            tcs_log("Error updating CSV data: " . $e->getMessage(), self::$log_target);
            return ['error' => "CSV update failed: " . $e->getMessage()];
        }
    }

    /**
     * Process HTML hilt template
     *
     * @param string $template_file Path to the template file
     * @param array $template_data Template data from form
     * @param array $files Uploaded files
     * @param string $key Route key
     * @return string Generated view content
     */
    private static function process_html_template(string $template_file, array $template_data, array $files, string $key): string {
        $html = $template_data['html'] ?? '';

        // Get template content
        $template_content = file_get_contents($template_file);

        // Replace placeholder with actual HTML
        $template_content = str_replace('/* HTML_CONTENT */', $html, $template_content);
        $template_content = str_replace('{{ $route_key }}', $key, $template_content);

        tcs_log("Successfully processed HTML hilt template for key: {$key}", self::$log_target);
        return $template_content;
    }

    /**
     * Process file upload hilt template
     *
     * @param string $template_file Path to the template file
     * @param array $template_data Template data from form
     * @param array $files Uploaded files
     * @param string $key Route key
     * @return string Generated view content
     */
    private static function process_file_upload_template(string $template_file, array $template_data, array $files, string $key): string {
        if (isset($files['template_file']) && $files['template_file']['error'] === UPLOAD_ERR_OK) {

            // Create directory if it doesn't exist
            if (!file_exists(FS_UPLOADS)) {
                mkdir(FS_UPLOADS, 0755, true);
            }

            $file_name = $key . '_' . basename($files['template_file']['name']);
            $upload_path = FS_UPLOADS . $file_name;

            if (move_uploaded_file($files['template_file']['tmp_name'], $upload_path)) {
                $file_content = file_get_contents($upload_path);
                $extension = pathinfo($upload_path, PATHINFO_EXTENSION);

                if ($extension === 'php') {
                    // For PHP files, use the content directly
                    return $file_content;
                } elseif ($extension === 'html') {
                    // For HTML files, wrap in PHP tags
                    return "<?php\n// Auto-generated hilt view from uploaded HTML file\n?>\n" . $file_content;
                } else {
                    // For other files, create a PHP file that outputs the content
                    return "<?php\n// Auto-generated hilt view from uploaded file\necho file_get_contents('" . $upload_path . "');\n";
                }
            }
        }

        // Default content if file upload failed
        return "<?php\n// Auto-generated hilt view (file upload failed)\necho '<div class=\"p-4 text-red-500\">No template file was uploaded or upload failed</div>';\n";
    }

    /**
     * Process default hilt template
     *
     * @param string $template_file Path to the template file
     * @param array $template_data Template data from form
     * @param array $files Uploaded files
     * @param string $key Route key
     * @return string Generated view content
     */
    private static function process_default_template(string $template_file, array $template_data, array $files, string $key): string {
        // Get template content
        $template_content = file_get_contents($template_file);

        // Replace placeholder with actual key
        $template_content = str_replace('/* PAGE_KEY */', $key, $template_content);
        $template_content = str_replace('{{ $route_key }}', $key, $template_content);

        tcs_log("Successfully processed default hilt template for key: {$key}", self::$log_target);
        return $template_content;
    }

    /**
     * Get data from hilt table for template rendering
     *
     * @param string $table_name Database table name
     * @param array $criteria Query criteria
     * @return array Query results
     */
    public static function get_table_data(string $table_name, array $criteria = []): array {
        try {
            $schema = database::schema();
            if (!$schema::hasTable($table_name)) {
                return ['error' => "Table {$table_name} does not exist"];
            }

            $query = database::table($table_name);

            // Apply criteria
            if (isset($criteria['limit'])) {
                $query->limit($criteria['limit']);
            }

            if (isset($criteria['offset'])) {
                $query->offset($criteria['offset']);
            }

            if (isset($criteria['search']) && !empty($criteria['search'])) {
                $query->where('data_json', 'LIKE', '%' . $criteria['search'] . '%');
            }

            $results = $query->get();

            // Decode JSON data
            $decoded_results = [];
            foreach ($results as $row) {
                $decoded_row = json_decode($row['data_json'], true);
                $decoded_row['id'] = $row['id'];
                $decoded_row['created_at'] = $row['created_at'];
                $decoded_row['updated_at'] = $row['updated_at'];
                $decoded_results[] = $decoded_row;
            }

            return ['success' => true, 'data' => $decoded_results];

        } catch (\system\DatabaseException $e) {
            // Enhanced logging for database-specific errors
            tcs_log([
                'action' => 'table_data_retrieval_failed',
                'table_name' => $table_name,
                'criteria' => $criteria,
                'error_message' => $e->getMessage(),
                'query' => $e->getQuery(),
                'parameters' => $e->getParams(),
                'detailed_message' => $e->getDetailedMessage()
            ], self::$log_target, true);
            return ['error' => "Failed to retrieve data: " . $e->getMessage()];
        } catch (\Exception $e) {
            // Fallback for non-database errors
            tcs_log("Error getting table data: " . $e->getMessage(), self::$log_target);
            return ['error' => "Failed to retrieve data: " . $e->getMessage()];
        }
    }

    /**
     * Create default hilt templates for different types
     *
     * @param string $template_file Template file path
     * @param string $template_type Type of template to create
     * @return void
     */
    public static function create_default_hilt_template(string $template_file, string $template_type = 'database'): void {
        // Create directory if it doesn't exist
        $dir = dirname($template_file);
        if (!file_exists($dir)) {
            mkdir($dir, 0755, true);
        }

        switch ($template_type) {
            case 'database':
            case 'csv':
                // Copy the database-driven template
                $default_template = FS_SYSTEM . 'templates/data_table_template.hilt.php';
                if (file_exists($default_template)) {
                    copy($default_template, $template_file);
                } else {
                    self::create_database_hilt_template($template_file);
                }
                break;

            case 'html':
            case 'custom_html':
                self::create_html_hilt_template($template_file);
                break;

            case 'file_upload':
                self::create_file_upload_hilt_template($template_file);
                break;

            case 'default':
            case 'none':
            default:
                self::create_basic_hilt_template($template_file);
                break;
        }
    }

    /**
     * Create database-driven hilt template
     */
    private static function create_database_hilt_template(string $template_file): void {
        $template_content = <<<'EOT'
@props([
    'name' => 'data-table database-driven',
    'description' => 'A database-driven data table using hilt system',
    'type' => 'hilt-database',
    'table_name' => '{{ $table_name }}',
    'route_key' => '{{ $route_key }}'
])

@php
use system\hilt;
use system\database;

$table_name = $table_name ?? 'autobooks_data';
$route_key = $route_key ?? 'default';

$search = $_GET['search'] ?? '';
$page = (int)($_GET['page'] ?? 1);
$per_page = (int)($_GET['per_page'] ?? 50);

$criteria = [
    'limit' => $per_page,
    'offset' => ($page - 1) * $per_page
];

if (!empty($search)) {
    $criteria['search'] = $search;
}

$data_result = hilt::get_table_data($table_name, $criteria);

if (isset($data_result['error'])) {
    echo '<div class="p-4 bg-red-100 border border-red-400 text-red-700 rounded">';
    echo '<h2 class="text-lg font-bold mb-2">Data Error</h2>';
    echo '<p>' . htmlspecialchars($data_result['error']) . '</p>';
    echo '</div>';
    return;
}

$items = $data_result['data'] ?? [];
@endphp

<div class="hilt-database-template">
    <h2>{{ ucwords(str_replace('_', ' ', $route_key)) }} Data</h2>

    @if(empty($items))
        <p>No data available. <a href="{{ APP_ROOT }}{{ $route_key }}.settings">Upload data</a></p>
    @else
        <div class="data-grid">
            @foreach($items as $item)
                <div class="data-item">
                    @foreach($item as $key => $value)
                        @if(!in_array($key, ['id', 'created_at', 'updated_at']))
                            <p><strong>{{ ucwords(str_replace('_', ' ', $key)) }}:</strong> {{ $value }}</p>
                        @endif
                    @endforeach
                </div>
            @endforeach
        </div>
    @endif
</div>
EOT;

        file_put_contents($template_file, $template_content);
    }

    /**
     * Create HTML hilt template
     */
    private static function create_html_hilt_template(string $template_file): void {
        $template_content = <<<'EOT'
@props([
    'name' => 'custom-html',
    'description' => 'A custom HTML hilt template',
    'type' => 'hilt-html',
    'route_key' => '{{ $route_key }}'
])

@php
// Custom HTML hilt template
// The HTML content will be replaced with actual content
$route_key = $route_key ?? 'default';
@endphp

<div class="hilt-html-template">
    <h1>{{ ucwords(str_replace('_', ' ', $route_key)) }}</h1>

    <!-- HTML_CONTENT -->
    /* HTML_CONTENT */

    <div class="template-info">
        <p><small>Hilt HTML Template - Route: {{ $route_key }}</small></p>
    </div>
</div>
EOT;

        file_put_contents($template_file, $template_content);
    }

    /**
     * Create file upload hilt template
     */
    private static function create_file_upload_hilt_template(string $template_file): void {
        $template_content = <<<'EOT'
@props([
    'name' => 'file-upload',
    'description' => 'A file upload hilt template',
    'type' => 'hilt-file-upload',
    'route_key' => '{{ $route_key }}'
])

@php
// File upload hilt template
$route_key = $route_key ?? 'default';
@endphp

<div class="hilt-file-upload-template">
    <h1>{{ ucwords(str_replace('_', ' ', $route_key)) }}</h1>

    <div class="upload-content">
        <!-- Content from uploaded file will be inserted here -->
        <p>Upload a file to replace this content.</p>
    </div>

    <div class="template-info">
        <p><small>Hilt File Upload Template - Route: {{ $route_key }}</small></p>
    </div>
</div>
EOT;

        file_put_contents($template_file, $template_content);
    }

    /**
     * Create basic hilt template
     */
    private static function create_basic_hilt_template(string $template_file): void {
        $template_content = <<<'EOT'
@props([
    'name' => 'basic-template',
    'description' => 'A basic hilt template',
    'type' => 'hilt-basic',
    'route_key' => '{{ $route_key }}'
])

@php
// Basic hilt template
$route_key = $route_key ?? 'default';
@endphp

<div class="hilt-basic-template">
    <div class="p-4">
        <h1 class="text-2xl font-bold mb-4">Welcome to {{ ucwords(str_replace('_', ' ', $route_key)) }}</h1>
        <p>This is a basic hilt template. Edit this file to customize the content.</p>

        <div class="mt-4">
            <p><strong>Route Key:</strong> {{ $route_key }}</p>
            <p><strong>Template Type:</strong> Hilt Basic</p>
        </div>
    </div>
</div>
EOT;

        file_put_contents($template_file, $template_content);
    }
}
