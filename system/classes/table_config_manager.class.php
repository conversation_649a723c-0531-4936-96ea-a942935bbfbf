<?php
namespace system;

use system\database;
use system\Schema;

/**
 * Table Configuration Manager
 * 
 * Manages persistent storage and retrieval of data table configurations
 * for performance optimization, similar to autodesk subscription table approach
 */
class table_config_manager {
    public static $log_target = "table_config_manager";
    
    private const CONFIG_TABLE = 'autobooks_table_configs';

    /**
     * Ensure the configuration table exists
     */
    public static function ensure_config_table_exists(): bool {
        try {
            if (!database::tableExists(self::CONFIG_TABLE)) {
                Schema::create(self::CONFIG_TABLE, function($table) {
                    $table->increments('id');
                    $table->string('table_name', 255);
                    $table->string('route_key', 255);
                    $table->json('table_schema');
                    $table->json('table_config');
                    $table->json('column_mappings');
                    $table->text('description')->nullable();
                    $table->string('data_source', 100)->default('csv'); // csv, api, manual
                    $table->boolean('is_active')->default(true);
                    $table->timestamps();
                    
                    // Add indexes for performance
                    $table->string('table_name')->unique();
                    $table->string('route_key');
                });
                
                tcs_log("Created table configuration storage table: " . self::CONFIG_TABLE, self::$log_target);
                return true;
            }
            return true;
        } catch (\Exception $e) {
            tcs_log("Failed to create config table: " . $e->getMessage(), self::$log_target);
            return false;
        }
    }

    /**
     * Store table configuration
     *
     * @param string $table_name Database table name
     * @param string $route_key Route identifier
     * @param array $schema Enhanced schema definition
     * @param array $config Generated table configuration
     * @param array $options Additional options
     * @return array Storage result
     */
    public static function store_table_config(string $table_name, string $route_key, array $schema, array $config, array $options = []): array {
        try {
            if (!self::ensure_config_table_exists()) {
                return ['error' => 'Failed to ensure config table exists'];
            }

            // Prepare column mappings for easy reference
            $column_mappings = [
                'csv_to_db' => $schema['mapping']['main']['columns'] ?? [],
                'data_types' => $schema['mapping']['main']['data_types'] ?? [],
                'column_labels' => []
            ];

            // Extract column labels from config
            foreach ($config['columns'] as $column) {
                $column_mappings['column_labels'][$column['field']] = $column['label'];
            }

            $config_data = [
                'table_name' => $table_name,
                'route_key' => $route_key,
                'table_schema' => json_encode($schema),
                'table_config' => json_encode($config),
                'column_mappings' => json_encode($column_mappings),
                'description' => $options['description'] ?? "Auto-generated configuration for {$table_name}",
                'data_source' => $options['data_source'] ?? 'csv',
                'is_active' => true,
                'created_at' => date('Y-m-d H:i:s'),
                'updated_at' => date('Y-m-d H:i:s')
            ];

            // Check if configuration already exists
            $existing = database::table(self::CONFIG_TABLE)
                ->where('table_name', $table_name)
                ->first();

            if ($existing) {
                // Update existing configuration
                $config_data['updated_at'] = date('Y-m-d H:i:s');
                database::table(self::CONFIG_TABLE)
                    ->where('table_name', $table_name)
                    ->update($config_data);
                
                tcs_log("Updated table configuration for: {$table_name}", self::$log_target);
                $action = 'updated';
            } else {
                // Insert new configuration
                database::table(self::CONFIG_TABLE)->insert($config_data);
                tcs_log("Stored new table configuration for: {$table_name}", self::$log_target);
                $action = 'created';
            }

            return [
                'success' => true,
                'message' => "Table configuration {$action} successfully",
                'table_name' => $table_name,
                'route_key' => $route_key,
                'action' => $action
            ];

        } catch (\Exception $e) {
            tcs_log("Error storing table config: " . $e->getMessage(), self::$log_target);
            return ['error' => "Failed to store table configuration: " . $e->getMessage()];
        }
    }

    /**
     * Retrieve stored table configuration
     *
     * @param string $table_name Database table name
     * @return array|null Configuration data or null if not found
     */
    public static function get_table_config(string $table_name): ?array {
        try {
            if (!database::tableExists(self::CONFIG_TABLE)) {
                return null;
            }

            $config_row = database::table(self::CONFIG_TABLE)
                ->where('table_name', $table_name)
                ->where('is_active', true)
                ->first();

            if (!$config_row) {
                return null;
            }

            return [
                'id' => $config_row['id'],
                'table_name' => $config_row['table_name'],
                'route_key' => $config_row['route_key'],
                'schema' => json_decode($config_row['table_schema'], true),
                'config' => json_decode($config_row['table_config'], true),
                'column_mappings' => json_decode($config_row['column_mappings'], true),
                'description' => $config_row['description'],
                'data_source' => $config_row['data_source'],
                'created_at' => $config_row['created_at'],
                'updated_at' => $config_row['updated_at']
            ];

        } catch (\Exception $e) {
            tcs_log("Error retrieving table config: " . $e->getMessage(), self::$log_target);
            return null;
        }
    }

    /**
     * Update table configuration with new data and criteria
     *
     * @param string $table_name Database table name
     * @param array $criteria Query criteria for data retrieval
     * @param array $options Display options
     * @return array Updated configuration or error
     */
    public static function get_updated_config_with_data(string $table_name, array $criteria = [], array $options = []): array {
        try {
            // Get stored configuration
            $stored_config = self::get_table_config($table_name);
            if (!$stored_config) {
                return ['error' => "No stored configuration found for table: {$table_name}"];
            }

            $base_config = $stored_config['config'];

            // Retrieve fresh data based on criteria
            $data_result = self::retrieve_table_data($table_name, $criteria);
            if (isset($data_result['error'])) {
                return $data_result;
            }

            // Update configuration with fresh data and criteria
            $updated_config = array_merge($base_config, [
                'items' => $data_result['data'],
                'items_per_page' => $options['items_per_page'] ?? $criteria['limit'] ?? 30,
                'current_page_num' => $options['current_page_num'] ?? (($criteria['offset'] ?? 0) / ($criteria['limit'] ?? 30)) + 1,
                'sort_column' => $criteria['order_by'] ?? '',
                'sort_direction' => $criteria['order_direction'] ?? 'asc',
                'callback' => $options['callback'] ?? 'data_table_filter',
                'just_body' => $options['just_body'] ?? false,
                'just_rows' => $options['just_rows'] ?? false
            ]);

            return ['success' => true, 'config' => $updated_config, 'stored_config' => $stored_config];

        } catch (\Exception $e) {
            tcs_log("Error updating config with data: " . $e->getMessage(), self::$log_target);
            return ['error' => "Failed to update configuration: " . $e->getMessage()];
        }
    }

    /**
     * Retrieve data from database table (similar to data_table_generator)
     */
    private static function retrieve_table_data(string $table_name, array $criteria = []): array {
        try {
            $query = database::table($table_name);

            // Apply WHERE conditions
            if (isset($criteria['where']) && is_array($criteria['where'])) {
                foreach ($criteria['where'] as $column => $condition) {
                    if (is_array($condition) && count($condition) >= 2) {
                        $operator = $condition[0];
                        $value = $condition[1];
                        $query->where($column, $operator, $value);
                    } else {
                        $query->where($column, $condition);
                    }
                }
            }

            // Apply search
            if (isset($criteria['search']) && !empty($criteria['search'])) {
                $search_term = $criteria['search'];
                $search_columns = $criteria['search_columns'] ?? [];
                
                if (!empty($search_columns)) {
                    $query->where(function($q) use ($search_columns, $search_term) {
                        foreach ($search_columns as $column) {
                            $q->orWhere($column, 'LIKE', "%{$search_term}%");
                        }
                    });
                }
            }

            // Apply ordering
            if (isset($criteria['order_by'])) {
                $direction = $criteria['order_direction'] ?? 'asc';
                $query->orderBy($criteria['order_by'], $direction);
            }

            // Apply pagination
            if (isset($criteria['limit'])) {
                $query->limit($criteria['limit']);
                
                if (isset($criteria['offset'])) {
                    $query->offset($criteria['offset']);
                }
            }

            $data = $query->get();

            return [
                'success' => true,
                'data' => $data,
                'count' => count($data)
            ];

        } catch (\Exception $e) {
            tcs_log("Error retrieving table data: " . $e->getMessage(), self::$log_target);
            return ['error' => "Failed to retrieve data: " . $e->getMessage()];
        }
    }

    /**
     * List all stored table configurations
     *
     * @return array List of configurations
     */
    public static function list_all_configs(): array {
        try {
            if (!database::tableExists(self::CONFIG_TABLE)) {
                return [];
            }

            $configs = database::table(self::CONFIG_TABLE)
                ->where('is_active', true)
                ->orderBy('table_name')
                ->get();

            return array_map(function($config) {
                return [
                    'table_name' => $config['table_name'],
                    'route_key' => $config['route_key'],
                    'description' => $config['description'],
                    'data_source' => $config['data_source'],
                    'created_at' => $config['created_at'],
                    'updated_at' => $config['updated_at']
                ];
            }, $configs);

        } catch (\Exception $e) {
            tcs_log("Error listing configs: " . $e->getMessage(), self::$log_target);
            return [];
        }
    }

    /**
     * Delete table configuration
     *
     * @param string $table_name Database table name
     * @return bool Success status
     */
    public static function delete_table_config(string $table_name): bool {
        try {
            if (!database::tableExists(self::CONFIG_TABLE)) {
                return true; // Nothing to delete
            }

            database::table(self::CONFIG_TABLE)
                ->where('table_name', $table_name)
                ->update(['is_active' => false, 'updated_at' => date('Y-m-d H:i:s')]);

            tcs_log("Deactivated table configuration for: {$table_name}", self::$log_target);
            return true;

        } catch (\Exception $e) {
            tcs_log("Error deleting table config: " . $e->getMessage(), self::$log_target);
            return false;
        }
    }
}
