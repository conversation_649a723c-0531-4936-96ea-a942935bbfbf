@props([
    'name' => 'Enhanced Data Table',
    'description' => 'A modern data table with automatic schema detection and Edge templating',
    'type' => 'csv',
    'table_name' => '{{ $table_name }}',
    'route_key' => '{{ $route_key }}',
    'auto_schema' => true,
    'show_analysis' => false
])

@php
// Set default values
$table_name = $table_name ?? 'autobooks_enhanced_data';
$route_key = $route_key ?? 'enhanced_data';
$auto_schema = $auto_schema ?? true;
$show_analysis = $show_analysis ?? false;

// Get request parameters
$search = $_GET['search'] ?? '';
$page = (int)($_GET['page'] ?? 1);
$per_page = (int)($_GET['per_page'] ?? 50);
$sort_column = $_GET['sort_column'] ?? '';
$sort_direction = $_GET['sort_direction'] ?? 'asc';

// Build criteria for data retrieval
$criteria = [
    'limit' => $per_page,
    'offset' => ($page - 1) * $per_page
];

if (!empty($search)) {
    $criteria['search'] = $search;
    // Add search columns based on table schema
    $criteria['search_columns'] = ['*']; // Will be refined by the generator
}

if (!empty($sort_column)) {
    $criteria['order_by'] = $sort_column;
    $criteria['order_direction'] = $sort_direction;
}

// Display options
$options = [
    'title' => 'Enhanced Data Table',
    'description' => 'Data from ' . $table_name,
    'items_per_page' => $per_page,
    'current_page_num' => $page,
    'sort_column' => $sort_column,
    'sort_direction' => $sort_direction,
    'callback' => 'data_table_filter',
    'class' => 'enhanced-data-table',
    'show_system_columns' => false,
    'hidden_columns' => ['data_hash'], // Hide internal columns
    'column_labels' => [] // Custom column labels can be added here
];

// Check if table exists
$table_exists = false;
$analysis_data = null;
$error_message = null;

try {
    // Check if table exists using a simple approach
    $table_exists = false;
    try {
        $test_query = database::table($table_name)->limit(1)->get();
        $table_exists = true;
    } catch (Exception $e) {
        $table_exists = false;
    }

    if (!$table_exists) {
        $error_message = "Table '{$table_name}' does not exist. Please upload CSV data to create it.";
    } else {
        // Use stored configuration for performance
        $config_result = table_config_manager::get_updated_config_with_data($table_name, $criteria, $options);

        if (isset($config_result['error'])) {
            // Fallback to generating config if stored config not found
            $table_result = data_table_generator::generate_table_config($table_name, $criteria, $options);
            if (isset($table_result['error'])) {
                $error_message = $table_result['error'];
            } else {
                $table_config = $table_result['config'];
            }
        } else {
            $table_config = $config_result['config'];
            $stored_config_info = $config_result['stored_config'];
        }

        // If showing analysis and we have CSV data, analyze it
        if ($show_analysis && isset($_FILES['csv_file']) && $_FILES['csv_file']['error'] === UPLOAD_ERR_OK) {
            $csv_file_path = $_FILES['csv_file']['tmp_name'];
            $analysis_result = data_importer::analyze_csv_structure($csv_file_path);
            if (isset($analysis_result['success'])) {
                $analysis_data = $analysis_result;
            }
        }
    }
} catch (Exception $e) {
    $error_message = "Error: " . $e->getMessage();
}
@endphp

<div class="enhanced-data-table-container">
    {{-- Header Section --}}
    <div class="mb-6">
        <h2 class="text-2xl font-bold text-gray-900">{{ $options['title'] }}</h2>
        <p class="text-gray-600">{{ $options['description'] }}</p>
        
        @if($auto_schema)
            <div class="mt-2 text-sm text-blue-600">
                <i class="fas fa-magic"></i> Auto-schema detection enabled
            </div>
        @endif

        @if(isset($stored_config_info))
            <div class="mt-2 text-sm text-green-600">
                <i class="fas fa-database"></i> Using stored configuration (created: {{ date('M j, Y', strtotime($stored_config_info['created_at'])) }})
            </div>
        @endif
    </div>

    {{-- Error Display --}}
    @if($error_message)
        <div class="bg-red-50 border border-red-200 rounded-md p-4 mb-6">
            <div class="flex">
                <div class="flex-shrink-0">
                    <i class="fas fa-exclamation-circle text-red-400"></i>
                </div>
                <div class="ml-3">
                    <h3 class="text-sm font-medium text-red-800">Error</h3>
                    <div class="mt-2 text-sm text-red-700">
                        {{ $error_message }}
                    </div>
                </div>
            </div>
        </div>
    @endif

    {{-- CSV Upload Section (shown when table doesn't exist or analysis is requested) --}}
    @if(!$table_exists || $show_analysis)
        <div class="bg-blue-50 border border-blue-200 rounded-md p-4 mb-6">
            <h3 class="text-lg font-medium text-blue-900 mb-3">
                @if(!$table_exists)
                    Create Table from CSV Data
                @else
                    Analyze CSV Structure
                @endif
            </h3>
            
            <form hx-post="api/enhanced_data/upload_csv" hx-target="#upload-result" hx-swap="innerHTML" enctype="multipart/form-data">
                <input type="hidden" name="table_name" value="{{ $table_name }}">
                <input type="hidden" name="route_key" value="{{ $route_key }}">
                <input type="hidden" name="auto_schema" value="{{ $auto_schema ? '1' : '0' }}">
                
                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                        <label for="csv_file" class="block text-sm font-medium text-gray-700">CSV File</label>
                        <input type="file" name="csv_file" id="csv_file" accept=".csv" 
                               class="mt-1 block w-full text-sm text-gray-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-blue-50 file:text-blue-700 hover:file:bg-blue-100">
                    </div>
                    
                    <div class="flex items-end">
                        <button type="submit" class="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md text-sm font-medium">
                            @if(!$table_exists)
                                <i class="fas fa-upload mr-2"></i>Create Table & Import
                            @else
                                <i class="fas fa-search mr-2"></i>Analyze Structure
                            @endif
                        </button>
                    </div>
                </div>
                
                @if($auto_schema)
                    <div class="mt-3">
                        <label class="flex items-center">
                            <input type="checkbox" name="replace_table" value="1" class="rounded border-gray-300 text-blue-600 shadow-sm focus:border-blue-300 focus:ring focus:ring-blue-200 focus:ring-opacity-50">
                            <span class="ml-2 text-sm text-gray-600">Replace existing table if it exists</span>
                        </label>
                    </div>
                @endif
            </form>
            
            <div id="upload-result" class="mt-4"></div>
        </div>
    @endif

    {{-- Analysis Results Display --}}
    @if($analysis_data)
        <div class="bg-green-50 border border-green-200 rounded-md p-4 mb-6">
            <h3 class="text-lg font-medium text-green-900 mb-3">CSV Analysis Results</h3>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div class="text-center">
                    <div class="text-2xl font-bold text-green-600">{{ count($analysis_data['headers']) }}</div>
                    <div class="text-sm text-gray-600">Columns</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-green-600">{{ $analysis_data['total_rows'] }}</div>
                    <div class="text-sm text-gray-600">Total Rows</div>
                </div>
                <div class="text-center">
                    <div class="text-2xl font-bold text-green-600">{{ $analysis_data['analyzed_rows'] }}</div>
                    <div class="text-sm text-gray-600">Analyzed Rows</div>
                </div>
            </div>
            
            <div class="overflow-x-auto">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Column</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Detected Type</th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">Sample Data</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        @foreach($analysis_data['headers'] as $header)
                            <tr>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">{{ $header }}</td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                        {{ $analysis_data['data_types'][$header] }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 text-sm text-gray-500">
                                    {{ implode(', ', array_slice($analysis_data['sample_data'][$header], 0, 3)) }}
                                    @if(count($analysis_data['sample_data'][$header]) > 3)
                                        <span class="text-gray-400">...</span>
                                    @endif
                                </td>
                            </tr>
                        @endforeach
                    </tbody>
                </table>
            </div>
        </div>
    @endif

    {{-- Data Table Section --}}
    @if($table_exists && !$error_message && isset($table_config))
        <div class="bg-white shadow rounded-lg">
            <div class="px-4 py-5 sm:p-6">
                {{-- Search and Controls --}}
                <div class="mb-4 flex flex-col sm:flex-row sm:items-center sm:justify-between">
                    <div class="flex-1 min-w-0">
                        <form hx-get="{{ $_SERVER['REQUEST_URI'] }}" hx-target="#data-table-container" hx-swap="innerHTML">
                            <div class="flex rounded-md shadow-sm">
                                <input type="text" name="search" value="{{ $search }}" 
                                       placeholder="Search..." 
                                       class="flex-1 min-w-0 block w-full px-3 py-2 rounded-none rounded-l-md border-gray-300 focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
                                <button type="submit" class="inline-flex items-center px-3 py-2 border border-l-0 border-gray-300 rounded-r-md bg-gray-50 text-gray-500 text-sm">
                                    <i class="fas fa-search"></i>
                                </button>
                            </div>
                        </form>
                    </div>
                    
                    <div class="mt-3 sm:mt-0 sm:ml-4">
                        <select name="per_page" onchange="window.location.href='?per_page=' + this.value + '&search={{ $search }}'" 
                                class="block w-full pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm rounded-md">
                            <option value="25" {{ $per_page == 25 ? 'selected' : '' }}>25 per page</option>
                            <option value="50" {{ $per_page == 50 ? 'selected' : '' }}>50 per page</option>
                            <option value="100" {{ $per_page == 100 ? 'selected' : '' }}>100 per page</option>
                        </select>
                    </div>
                </div>

                {{-- Data Table --}}
                <div id="data-table-container">
                    <x-data-table
                        :title="$table_config['title']"
                        :description="$table_config['description']"
                        :items="$table_config['items']"
                        :columns="$table_config['columns']"
                        :rows="$table_config['rows']"
                        :just_body="$table_config['just_body']"
                        :just_rows="$table_config['just_rows']"
                        :items_per_page="$table_config['items_per_page']"
                        :current_page_num="$table_config['current_page_num']"
                        :sort_column="$table_config['sort_column']"
                        :sort_direction="$table_config['sort_direction']"
                        :callback="$table_config['callback']"
                        :class="$table_config['class']"
                    />
                </div>
            </div>
        </div>
    @endif

    {{-- Help Section --}}
    @if(!$table_exists)
        <div class="mt-6 bg-gray-50 border border-gray-200 rounded-md p-4">
            <h3 class="text-lg font-medium text-gray-900 mb-2">Getting Started</h3>
            <div class="text-sm text-gray-600 space-y-2">
                <p>1. Upload a CSV file using the form above</p>
                <p>2. The system will automatically analyze the data types in your CSV</p>
                <p>3. A database table will be created with appropriate column types</p>
                <p>4. Your data will be imported and displayed in the table below</p>
            </div>
        </div>
    @endif
</div>

{{-- JavaScript for enhanced functionality --}}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Auto-refresh functionality
    const autoRefresh = document.querySelector('[data-auto-refresh]');
    if (autoRefresh) {
        setInterval(() => {
            htmx.trigger('#data-table-container', 'refresh');
        }, 30000); // Refresh every 30 seconds
    }
    
    // Enhanced search with debouncing
    const searchInput = document.querySelector('input[name="search"]');
    if (searchInput) {
        let searchTimeout;
        searchInput.addEventListener('input', function() {
            clearTimeout(searchTimeout);
            searchTimeout = setTimeout(() => {
                htmx.trigger(this.closest('form'), 'submit');
            }, 500);
        });
    }
});
</script>
