<?php
/**
 * Test script to verify that enhanced import is always used
 */

// Define constants needed
define('DEBUG_MODE', true);
define('FS_SYS_LOGS', 'system/logs/');
define('FS_SYSTEM', __DIR__ . '/system/');
define('FS_APP_ROOT', __DIR__ . '/');
define('DOMAIN', 'localhost');

// Include necessary files
require_once 'system/classes/database.class.php';
require_once 'system/classes/data_importer.class.php';
require_once 'system/classes/hilt.class.php';
require_once 'system/classes/table_config_manager.class.php';
require_once 'system/functions/database.php';
require_once 'system/functions/functions.php';

use system\database;
use system\data_importer;
use system\hilt;
use system\table_config_manager;

echo "<h1>Always Enhanced Import Test</h1>\n";

// Create sample CSV data
$sample_csv_content = "name,age,email,salary,hire_date,is_active\n";
$sample_csv_content .= "<PERSON>,30,<EMAIL>,50000.00,2023-01-15,true\n";
$sample_csv_content .= "<PERSON>,25,<EMAIL>,45000.50,2023-02-20,true\n";
$sample_csv_content .= "Bob Johnson,35,<EMAIL>,60000.75,2023-03-10,false\n";

$test_csv_file = tempnam(sys_get_temp_dir(), 'always_enhanced_test_') . '.csv';
file_put_contents($test_csv_file, $sample_csv_content);

echo "<h2>Sample CSV Data:</h2>\n";
echo "<pre>" . htmlspecialchars($sample_csv_content) . "</pre>\n";

$timestamp = time();

try {
    echo "<h2>Test 1: Direct data_importer::import_csv_to_hilt_table() Call</h2>\n";
    
    $test_table_1 = "always_enhanced_test_1_{$timestamp}";
    $result_1 = data_importer::import_csv_to_hilt_table($test_table_1, $test_csv_file, true, false);
    
    if (isset($result_1['error'])) {
        echo "❌ Test 1 failed: " . $result_1['error'] . "\n";
    } else {
        echo "✅ Test 1 successful!\n";
        
        // Check table structure
        if (database::tableExists($test_table_1)) {
            $describe_1 = "DESCRIBE `{$test_table_1}`";
            $columns_1 = database::rawQuery($describe_1)->fetchAll();
            
            echo "<h3>Table Structure:</h3>\n";
            echo "<table border='1' style='border-collapse: collapse;'>\n";
            echo "<tr><th>Column</th><th>Type</th><th>Null</th><th>Key</th></tr>\n";
            
            $has_typed_columns = false;
            $has_json_column = false;
            
            foreach ($columns_1 as $col) {
                echo "<tr><td>{$col['Field']}</td><td>{$col['Type']}</td><td>{$col['Null']}</td><td>{$col['Key']}</td></tr>\n";
                
                if (in_array($col['Field'], ['name', 'age', 'email', 'salary', 'hire_date', 'is_active'])) {
                    $has_typed_columns = true;
                }
                if ($col['Field'] === 'data_json') {
                    $has_json_column = true;
                }
            }
            echo "</table>\n";
            
            if ($has_typed_columns && !$has_json_column) {
                echo "<p>✅ <strong>SUCCESS:</strong> Table has individual typed columns (no JSON storage)</p>\n";
            } elseif ($has_json_column) {
                echo "<p>❌ <strong>FAILURE:</strong> Table still uses JSON storage</p>\n";
            } else {
                echo "<p>⚠️ <strong>UNCLEAR:</strong> Could not determine table structure</p>\n";
            }
            
            // Check stored configuration
            $stored_config_1 = table_config_manager::get_table_config($test_table_1);
            if ($stored_config_1) {
                echo "<p>✅ Configuration stored successfully</p>\n";
            } else {
                echo "<p>❌ No stored configuration found</p>\n";
            }
        } else {
            echo "<p>❌ Table was not created</p>\n";
        }
    }
    
    echo "<h2>Test 2: Hilt Class Import</h2>\n";
    
    $test_table_2 = "always_enhanced_test_2_{$timestamp}";
    
    // Simulate file upload array
    $files = [
        'csv_file' => [
            'tmp_name' => $test_csv_file,
            'error' => UPLOAD_ERR_OK
        ]
    ];
    
    $template_data = [
        'table_name' => $test_table_2
    ];
    
    $result_2 = hilt::import_csv_data($test_table_2, $template_data, $files);
    
    if (isset($result_2['error'])) {
        echo "❌ Test 2 failed: " . $result_2['error'] . "\n";
    } else {
        echo "✅ Test 2 successful!\n";
        
        // Check table structure
        if (database::tableExists($test_table_2)) {
            $describe_2 = "DESCRIBE `{$test_table_2}`";
            $columns_2 = database::rawQuery($describe_2)->fetchAll();
            
            echo "<h3>Table Structure:</h3>\n";
            echo "<table border='1' style='border-collapse: collapse;'>\n";
            echo "<tr><th>Column</th><th>Type</th><th>Null</th><th>Key</th></tr>\n";
            
            $has_typed_columns = false;
            $has_json_column = false;
            
            foreach ($columns_2 as $col) {
                echo "<tr><td>{$col['Field']}</td><td>{$col['Type']}</td><td>{$col['Null']}</td><td>{$col['Key']}</td></tr>\n";
                
                if (in_array($col['Field'], ['name', 'age', 'email', 'salary', 'hire_date', 'is_active'])) {
                    $has_typed_columns = true;
                }
                if ($col['Field'] === 'data_json') {
                    $has_json_column = true;
                }
            }
            echo "</table>\n";
            
            if ($has_typed_columns && !$has_json_column) {
                echo "<p>✅ <strong>SUCCESS:</strong> Hilt import uses individual typed columns</p>\n";
            } elseif ($has_json_column) {
                echo "<p>❌ <strong>FAILURE:</strong> Hilt import still uses JSON storage</p>\n";
            } else {
                echo "<p>⚠️ <strong>UNCLEAR:</strong> Could not determine table structure</p>\n";
            }
        } else {
            echo "<p>❌ Table was not created by Hilt import</p>\n";
        }
    }
    
    echo "<h2>Test 3: String Data Import (Textarea)</h2>\n";
    
    $test_table_3 = "always_enhanced_test_3_{$timestamp}";
    
    $template_data_3 = [
        'table_name' => $test_table_3,
        'csv_data' => $sample_csv_content
    ];
    
    $result_3 = hilt::import_csv_data($test_table_3, $template_data_3, []);
    
    if (isset($result_3['error'])) {
        echo "❌ Test 3 failed: " . $result_3['error'] . "\n";
    } else {
        echo "✅ Test 3 successful!\n";
        
        // Check table structure
        if (database::tableExists($test_table_3)) {
            $describe_3 = "DESCRIBE `{$test_table_3}`";
            $columns_3 = database::rawQuery($describe_3)->fetchAll();
            
            $has_typed_columns = false;
            $has_json_column = false;
            
            foreach ($columns_3 as $col) {
                if (in_array($col['Field'], ['name', 'age', 'email', 'salary', 'hire_date', 'is_active'])) {
                    $has_typed_columns = true;
                }
                if ($col['Field'] === 'data_json') {
                    $has_json_column = true;
                }
            }
            
            if ($has_typed_columns && !$has_json_column) {
                echo "<p>✅ <strong>SUCCESS:</strong> String data import uses individual typed columns</p>\n";
            } elseif ($has_json_column) {
                echo "<p>❌ <strong>FAILURE:</strong> String data import still uses JSON storage</p>\n";
            } else {
                echo "<p>⚠️ <strong>UNCLEAR:</strong> Could not determine table structure</p>\n";
            }
        } else {
            echo "<p>❌ Table was not created by string data import</p>\n";
        }
    }
    
    echo "<h2>Summary</h2>\n";
    
    $all_tests_passed = true;
    $test_results = [];
    
    // Check all test tables
    $test_tables = [$test_table_1, $test_table_2, $test_table_3];
    foreach ($test_tables as $i => $table) {
        $test_num = $i + 1;
        if (database::tableExists($table)) {
            $columns = database::rawQuery("DESCRIBE `{$table}`")->fetchAll();
            $has_typed = false;
            $has_json = false;
            
            foreach ($columns as $col) {
                if (in_array($col['Field'], ['name', 'age', 'email', 'salary', 'hire_date', 'is_active'])) {
                    $has_typed = true;
                }
                if ($col['Field'] === 'data_json') {
                    $has_json = true;
                }
            }
            
            $test_results[$test_num] = [
                'typed_columns' => $has_typed,
                'json_column' => $has_json,
                'passed' => $has_typed && !$has_json
            ];
            
            if (!$test_results[$test_num]['passed']) {
                $all_tests_passed = false;
            }
        } else {
            $test_results[$test_num] = ['passed' => false];
            $all_tests_passed = false;
        }
    }
    
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>\n";
    echo "<tr><th>Test</th><th>Method</th><th>Typed Columns</th><th>JSON Column</th><th>Result</th></tr>\n";
    echo "<tr><td>1</td><td>Direct import_csv_to_hilt_table()</td><td>" . ($test_results[1]['typed_columns'] ? 'Yes' : 'No') . "</td><td>" . ($test_results[1]['json_column'] ? 'Yes' : 'No') . "</td><td>" . ($test_results[1]['passed'] ? '✅ PASS' : '❌ FAIL') . "</td></tr>\n";
    echo "<tr><td>2</td><td>Hilt file import</td><td>" . ($test_results[2]['typed_columns'] ? 'Yes' : 'No') . "</td><td>" . ($test_results[2]['json_column'] ? 'Yes' : 'No') . "</td><td>" . ($test_results[2]['passed'] ? '✅ PASS' : '❌ FAIL') . "</td></tr>\n";
    echo "<tr><td>3</td><td>Hilt string import</td><td>" . ($test_results[3]['typed_columns'] ? 'Yes' : 'No') . "</td><td>" . ($test_results[3]['json_column'] ? 'Yes' : 'No') . "</td><td>" . ($test_results[3]['passed'] ? '✅ PASS' : '❌ FAIL') . "</td></tr>\n";
    echo "</table>\n";
    
    if ($all_tests_passed) {
        echo "<h3>🎉 ALL TESTS PASSED!</h3>\n";
        echo "<p><strong>Enhanced import with typed columns is now always used.</strong></p>\n";
    } else {
        echo "<h3>❌ SOME TESTS FAILED</h3>\n";
        echo "<p>The system is still using JSON storage in some cases.</p>\n";
    }
    
} catch (Exception $e) {
    echo "<h2>❌ Error during testing:</h2>\n";
    echo "<p>" . $e->getMessage() . "</p>\n";
    echo "<pre>" . $e->getTraceAsString() . "</pre>\n";
} finally {
    // Cleanup
    echo "<h2>Cleanup</h2>\n";
    $cleanup_tables = [
        "always_enhanced_test_1_{$timestamp}",
        "always_enhanced_test_2_{$timestamp}",
        "always_enhanced_test_3_{$timestamp}"
    ];
    
    foreach ($cleanup_tables as $table) {
        try {
            if (database::tableExists($table)) {
                database::rawQuery("DROP TABLE `{$table}`");
                echo "<p>✅ Cleaned up table: {$table}</p>\n";
            }
        } catch (Exception $e) {
            echo "<p>⚠️ Cleanup error for {$table}: " . $e->getMessage() . "</p>\n";
        }
    }
    
    if (file_exists($test_csv_file)) {
        unlink($test_csv_file);
        echo "<p>✅ Cleaned up temporary CSV file</p>\n";
    }
}

echo "<h2>Key Changes Made</h2>\n";
echo "<ul>\n";
echo "<li>✅ <code>import_csv_to_hilt_table()</code> now always uses enhanced import internally</li>\n";
echo "<li>✅ Hilt class always uses enhanced import for all CSV operations</li>\n";
echo "<li>✅ Enhanced Data API always uses enhanced import</li>\n";
echo "<li>✅ Old JSON storage approach is completely removed from active code paths</li>\n";
echo "<li>✅ All imports now create individual typed columns automatically</li>\n";
echo "<li>✅ Table configurations are automatically stored for performance</li>\n";
echo "</ul>\n";
?>
