<?php
// Test script for the new count() method implementation

// Define constants needed
define('DEBUG_MODE', true);
define('FS_SYS_LOGS', 'system/logs/');
define('FS_SYSTEM', __DIR__ . '/system/');
define('FS_APP_ROOT', __DIR__ . '/');
define('DOMAIN', 'localhost');

// Include necessary files
require_once 'system/classes/database.class.php';
require_once 'system/functions/database.php';
require_once 'system/functions/functions.php';

use system\database;

echo "<h1>Testing database::table()->count() Method</h1>\n";

try {
    // Test 1: Basic count without conditions
    echo "<h2>Test 1: Basic Count</h2>\n";
    $count = database::table('autobooks_navigation')->count();
    echo "✓ Basic count successful: {$count} rows\n";
    
    // Test 2: Count with WHERE condition
    echo "<h2>Test 2: Count with WHERE condition</h2>\n";
    $count_with_where = database::table('autobooks_navigation')
        ->where('status', 'active')
        ->count();
    echo "✓ Count with WHERE successful: {$count_with_where} rows\n";
    
    // Test 3: Verify the count method returns an integer
    echo "<h2>Test 3: Return Type Verification</h2>\n";
    $count_type = database::table('autobooks_navigation')->count();
    if (is_int($count_type)) {
        echo "✓ Count method returns integer: " . gettype($count_type) . "\n";
    } else {
        echo "✗ Count method should return integer, got: " . gettype($count_type) . "\n";
    }
    
    // Test 4: Compare with actual data retrieval
    echo "<h2>Test 4: Verification against actual data</h2>\n";
    $actual_data = database::table('autobooks_navigation')->get();
    $actual_count = count($actual_data);
    $db_count = database::table('autobooks_navigation')->count();
    
    if ($actual_count === $db_count) {
        echo "✓ Count matches actual data: {$db_count} rows\n";
    } else {
        echo "✗ Count mismatch - DB count: {$db_count}, Actual: {$actual_count}\n";
    }
    
    echo "<h2>All Tests Completed Successfully!</h2>\n";
    
} catch (Exception $e) {
    echo "✗ Error during testing: " . $e->getMessage() . "\n";
    echo "<pre>" . $e->getTraceAsString() . "</pre>\n";
}
?>
