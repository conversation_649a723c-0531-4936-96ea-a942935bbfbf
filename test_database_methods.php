<?php
/**
 * Test script for database methods including the new exists() method
 */

// Define constants needed
define('DEBUG_MODE', true);
define('FS_SYS_LOGS', 'system/logs/');
define('FS_SYSTEM', __DIR__ . '/system/');
define('FS_APP_ROOT', __DIR__ . '/');
define('DOMAIN', 'localhost');

// Include necessary files
require_once 'system/classes/database.class.php';
require_once 'system/functions/database.php';
require_once 'system/functions/functions.php';

use system\database;

echo "<h1>Database Methods Test</h1>\n";

try {
    echo "<h2>Testing tableExists() method</h2>\n";
    
    // Test with a table that should exist (users table is common)
    $test_tables = ['users', 'nonexistent_table_12345', 'information_schema'];
    
    foreach ($test_tables as $table) {
        $exists = database::tableExists($table);
        $status = $exists ? '✅ EXISTS' : '❌ NOT EXISTS';
        echo "<p>Table '{$table}': {$status}</p>\n";
    }
    
    echo "<h2>Testing exists() method on query builder</h2>\n";
    
    // Test exists method on a query
    try {
        $user_exists = database::table('users')->limit(1)->exists();
        $status = $user_exists ? '✅ HAS RECORDS' : '❌ NO RECORDS';
        echo "<p>Users table has records: {$status}</p>\n";
    } catch (Exception $e) {
        echo "<p>❌ Error testing users table: " . $e->getMessage() . "</p>\n";
    }
    
    // Test exists with WHERE condition
    try {
        $specific_user = database::table('users')->where('id', 1)->exists();
        $status = $specific_user ? '✅ EXISTS' : '❌ NOT EXISTS';
        echo "<p>User with ID 1: {$status}</p>\n";
    } catch (Exception $e) {
        echo "<p>⚠️ Could not test specific user (table might not exist): " . $e->getMessage() . "</p>\n";
    }
    
    echo "<h2>Testing with nonexistent table</h2>\n";
    
    try {
        $nonexistent = database::table('definitely_not_a_real_table_name')->exists();
        echo "<p>❌ This should have thrown an error</p>\n";
    } catch (Exception $e) {
        echo "<p>✅ Correctly caught error for nonexistent table: " . $e->getMessage() . "</p>\n";
    }
    
    echo "<h2>Performance Test</h2>\n";
    
    $start_time = microtime(true);
    for ($i = 0; $i < 10; $i++) {
        database::tableExists('users');
    }
    $end_time = microtime(true);
    $duration = ($end_time - $start_time) * 1000; // Convert to milliseconds
    
    echo "<p>✅ 10 tableExists() calls took: " . number_format($duration, 2) . " ms</p>\n";
    
    echo "<h2>✅ All Database Method Tests Completed!</h2>\n";
    
} catch (Exception $e) {
    echo "<h2>❌ Test failed with error:</h2>\n";
    echo "<p>" . $e->getMessage() . "</p>\n";
    echo "<pre>" . $e->getTraceAsString() . "</pre>\n";
}

echo "<h2>Test Summary</h2>\n";
echo "<p>This test verifies:</p>\n";
echo "<ul>\n";
echo "<li>✅ database::tableExists() method works correctly</li>\n";
echo "<li>✅ QueryBuilder::exists() method works correctly</li>\n";
echo "<li>✅ Proper error handling for nonexistent tables</li>\n";
echo "<li>✅ Performance is acceptable for table existence checks</li>\n";
echo "</ul>\n";
?>
