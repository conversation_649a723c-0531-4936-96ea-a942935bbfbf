<?php
/**
 * Test script for enhanced data import functionality
 */

// Define constants needed
define('DEBUG_MODE', true);
define('FS_SYS_LOGS', 'system/logs/');
define('FS_SYSTEM', __DIR__ . '/system/');
define('FS_APP_ROOT', __DIR__ . '/');
define('DOMAIN', 'localhost');

// Include necessary files
require_once 'system/classes/database.class.php';
require_once 'system/classes/data_importer.class.php';
require_once 'system/classes/data_table_generator.class.php';
require_once 'system/classes/table_config_manager.class.php';
require_once 'system/definitions/data_type_definitions.php';
require_once 'system/functions/database.php';
require_once 'system/functions/functions.php';

use system\database;
use system\data_importer;
use system\data_table_generator;
use system\table_config_manager;
use system\definitions\data_type_definitions;

echo "<h1>Enhanced Data Import Test</h1>\n";

// Create a sample CSV file for testing
$sample_csv_content = "name,age,email,salary,hire_date,is_active\n";
$sample_csv_content .= "<PERSON>,30,<EMAIL>,50000.00,2023-01-15,true\n";
$sample_csv_content .= "Jane Smith,25,<EMAIL>,45000.50,2023-02-20,true\n";
$sample_csv_content .= "Bob Johnson,35,<EMAIL>,60000.75,2023-03-10,false\n";
$sample_csv_content .= "Alice Brown,28,<EMAIL>,52000.25,2023-04-05,true\n";

$test_csv_file = tempnam(sys_get_temp_dir(), 'enhanced_test_') . '.csv';
file_put_contents($test_csv_file, $sample_csv_content);

echo "<h2>Sample CSV Content:</h2>\n";
echo "<pre>" . htmlspecialchars($sample_csv_content) . "</pre>\n";

$test_table = "enhanced_test_" . time();

try {
    echo "<h2>Step 1: Analyzing CSV Structure</h2>\n";
    
    $analysis = data_importer::analyze_csv_structure($test_csv_file);
    
    if (isset($analysis['error'])) {
        echo "❌ Analysis failed: " . $analysis['error'] . "\n";
        exit;
    }
    
    echo "✅ Analysis successful!\n";
    echo "<h3>Analysis Results:</h3>\n";
    echo "<ul>\n";
    echo "<li>Headers: " . implode(', ', $analysis['headers']) . "</li>\n";
    echo "<li>Total rows: " . $analysis['total_rows'] . "</li>\n";
    echo "<li>Analyzed rows: " . $analysis['analyzed_rows'] . "</li>\n";
    echo "</ul>\n";
    
    echo "<h3>Detected Data Types:</h3>\n";
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>\n";
    echo "<tr><th>Column</th><th>Detected Type</th><th>Sample Data</th></tr>\n";
    foreach ($analysis['headers'] as $header) {
        $type = $analysis['data_types'][$header];
        $sample = implode(', ', array_slice($analysis['sample_data'][$header], 0, 3));
        echo "<tr><td>{$header}</td><td>{$type}</td><td>{$sample}</td></tr>\n";
    }
    echo "</table>\n";
    
    echo "<h2>Step 2: Generating Enhanced Schema</h2>\n";
    
    $schema_result = data_importer::generate_enhanced_schema($analysis, $test_table);
    
    if (isset($schema_result['error'])) {
        echo "❌ Schema generation failed: " . $schema_result['error'] . "\n";
        exit;
    }
    
    echo "✅ Schema generation successful!\n";
    $schema = $schema_result['schema'];
    
    echo "<h3>Generated Schema:</h3>\n";
    echo "<pre>" . print_r($schema, true) . "</pre>\n";
    
    echo "<h2>Step 3: Creating Table with Enhanced Schema</h2>\n";
    
    $create_result = data_importer::create_table_from_schema($schema);
    
    if (isset($create_result['error'])) {
        echo "❌ Table creation failed: " . $create_result['error'] . "\n";
        exit;
    }
    
    echo "✅ Table creation successful!\n";
    echo "<p>Table '{$test_table}' created with enhanced schema.</p>\n";
    
    echo "<h2>Step 4: Importing Data with Enhanced Schema</h2>\n";
    
    $import_result = data_importer::import_csv_with_auto_schema($test_csv_file, $test_table, true, false);
    
    if (isset($import_result['error'])) {
        echo "❌ Enhanced import failed: " . $import_result['error'] . "\n";
        // Clean up
        try {
            database::schema()::drop($test_table);
        } catch (Exception $e) {
            // Ignore cleanup errors
        }
        exit;
    }
    
    echo "✅ Enhanced import successful!\n";
    echo "<p>Data imported successfully into table '{$test_table}'.</p>\n";
    
    echo "<h2>Step 5: Testing Stored Configuration System</h2>\n";

    // Test retrieving stored configuration
    $stored_config = table_config_manager::get_table_config($test_table);

    if ($stored_config) {
        echo "✅ Stored configuration retrieved successfully!\n";
        echo "<h3>Stored Configuration Details:</h3>\n";
        echo "<ul>\n";
        echo "<li>Route Key: " . $stored_config['route_key'] . "</li>\n";
        echo "<li>Data Source: " . $stored_config['data_source'] . "</li>\n";
        echo "<li>Created: " . $stored_config['created_at'] . "</li>\n";
        echo "<li>Columns in config: " . count($stored_config['config']['columns']) . "</li>\n";
        echo "</ul>\n";
    } else {
        echo "❌ No stored configuration found\n";
    }

    echo "<h2>Step 6: Testing Data Table Generator with Stored Config</h2>\n";

    $criteria = ['limit' => 10];
    $options = [
        'title' => 'Enhanced Test Data',
        'description' => 'Test data imported with enhanced schema detection'
    ];

    // Test using stored configuration
    $config_result = table_config_manager::get_updated_config_with_data($test_table, $criteria, $options);

    if (isset($config_result['error'])) {
        echo "⚠️ Stored config not available, falling back to generator\n";
        $table_config_result = data_table_generator::generate_table_config($test_table, $criteria, $options);
    } else {
        echo "✅ Using stored configuration for performance!\n";
        $table_config_result = ['success' => true, 'config' => $config_result['config']];
    }
    
    if (isset($table_config_result['error'])) {
        echo "❌ Data table generation failed: " . $table_config_result['error'] . "\n";
    } else {
        echo "✅ Data table generation successful!\n";
        $config = $table_config_result['config'];
        
        echo "<h3>Generated Table Configuration:</h3>\n";
        echo "<p>Title: " . $config['title'] . "</p>\n";
        echo "<p>Columns: " . count($config['columns']) . "</p>\n";
        echo "<p>Data rows: " . count($config['items']) . "</p>\n";
        
        echo "<h3>Column Definitions:</h3>\n";
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>\n";
        echo "<tr><th>Label</th><th>Field</th><th>Filter</th><th>Sortable</th></tr>\n";
        foreach ($config['columns'] as $column) {
            $filter = $column['filter'] ? 'Yes' : 'No';
            $sortable = $column['sortable'] ? 'Yes' : 'No';
            echo "<tr><td>{$column['label']}</td><td>{$column['field']}</td><td>{$filter}</td><td>{$sortable}</td></tr>\n";
        }
        echo "</table>\n";
        
        echo "<h3>Sample Data:</h3>\n";
        if (!empty($config['items'])) {
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>\n";
            
            // Header row
            echo "<tr>";
            foreach ($config['columns'] as $column) {
                echo "<th>" . $column['label'] . "</th>";
            }
            echo "</tr>\n";
            
            // Data rows (first 3)
            foreach (array_slice($config['items'], 0, 3) as $item) {
                echo "<tr>";
                foreach ($config['columns'] as $column) {
                    $value = $item[$column['field']] ?? '';
                    echo "<td>" . htmlspecialchars($value) . "</td>";
                }
                echo "</tr>\n";
            }
            echo "</table>\n";
        }
    }
    
    echo "<h2>Step 7: Verifying Typed Columns in Database</h2>\n";

    // Check the actual database structure
    try {
        $describe_query = "DESCRIBE `{$test_table}`";
        $columns_result = database::rawQuery($describe_query);
        $columns = $columns_result->fetchAll(\PDO::FETCH_ASSOC);

        echo "✅ Database table structure verified!\n";
        echo "<h3>Actual Database Columns:</h3>\n";
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>\n";
        echo "<tr><th>Column</th><th>Type</th><th>Null</th><th>Key</th><th>Default</th></tr>\n";
        foreach ($columns as $column) {
            echo "<tr>";
            echo "<td>{$column['Field']}</td>";
            echo "<td>{$column['Type']}</td>";
            echo "<td>{$column['Null']}</td>";
            echo "<td>{$column['Key']}</td>";
            echo "<td>{$column['Default']}</td>";
            echo "</tr>\n";
        }
        echo "</table>\n";

        // Verify data is stored in typed columns, not JSON
        $sample_data = database::table($test_table)->limit(1)->first();
        if ($sample_data) {
            echo "<h3>Sample Data (Typed Columns):</h3>\n";
            echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>\n";
            echo "<tr>";
            foreach ($sample_data as $key => $value) {
                echo "<th>{$key}</th>";
            }
            echo "</tr>\n<tr>";
            foreach ($sample_data as $key => $value) {
                echo "<td>" . htmlspecialchars($value ?? 'NULL') . "</td>";
            }
            echo "</tr>\n</table>\n";

            echo "✅ Data is stored in individual typed columns (not JSON)!\n";
        }

    } catch (Exception $e) {
        echo "❌ Error verifying database structure: " . $e->getMessage() . "\n";
    }

    echo "<h2>Step 8: Testing Data Type Definitions</h2>\n";
    
    echo "<h3>Supported Data Types:</h3>\n";
    $types_with_descriptions = data_type_definitions::get_all_types_with_descriptions();
    echo "<ul>\n";
    foreach ($types_with_descriptions as $type => $description) {
        echo "<li><strong>{$type}</strong>: {$description}</li>\n";
    }
    echo "</ul>\n";
    
    echo "<h3>Column Name Sanitization Test:</h3>\n";
    $test_names = ['User Name', 'email-address', '123invalid', 'valid_column', 'Special@#$%Characters'];
    echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>\n";
    echo "<tr><th>Original</th><th>Sanitized</th><th>Valid</th></tr>\n";
    foreach ($test_names as $name) {
        $validation = data_type_definitions::validate_column_name($name);
        $valid = $validation['valid'] ? 'Yes' : 'No';
        echo "<tr><td>{$name}</td><td>{$validation['sanitized']}</td><td>{$valid}</td></tr>\n";
    }
    echo "</table>\n";
    
    echo "<h2>✅ All Tests Completed Successfully!</h2>\n";
    
    // Clean up
    echo "<h2>Cleanup</h2>\n";
    try {
        database::schema()::drop($test_table);
        echo "✅ Test table cleaned up successfully.\n";
    } catch (Exception $e) {
        echo "⚠️ Cleanup warning: " . $e->getMessage() . "\n";
    }
    
    // Clean up temp file
    unlink($test_csv_file);
    echo "✅ Temporary CSV file cleaned up.\n";
    
} catch (Exception $e) {
    echo "❌ Test failed with error: " . $e->getMessage() . "\n";
    echo "<pre>" . $e->getTraceAsString() . "</pre>\n";
    
    // Attempt cleanup
    try {
        if (isset($test_table)) {
            database::schema()::drop($test_table);
        }
        if (file_exists($test_csv_file)) {
            unlink($test_csv_file);
        }
    } catch (Exception $cleanup_e) {
        echo "⚠️ Cleanup error: " . $cleanup_e->getMessage() . "\n";
    }
}

echo "<h2>Test Summary</h2>\n";
echo "<p>This test demonstrates the enhanced data import functionality including:</p>\n";
echo "<ul>\n";
echo "<li>✅ CSV structure analysis with automatic data type detection</li>\n";
echo "<li>✅ Enhanced schema generation with proper column types</li>\n";
echo "<li>✅ Automatic database table creation using Blueprint</li>\n";
echo "<li>✅ Data import with type-aware column mapping</li>\n";
echo "<li>✅ Modern Edge-based data table generation</li>\n";
echo "<li>✅ Data type definitions and column name sanitization</li>\n";
echo "</ul>\n";
?>
