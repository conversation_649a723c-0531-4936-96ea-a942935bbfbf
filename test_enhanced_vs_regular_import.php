<?php
/**
 * Test script to compare enhanced import vs regular import
 */

// Define constants needed
define('DEBUG_MODE', true);
define('FS_SYS_LOGS', 'system/logs/');
define('FS_SYSTEM', __DIR__ . '/system/');
define('FS_APP_ROOT', __DIR__ . '/');
define('DOMAIN', 'localhost');

// Include necessary files
require_once 'system/classes/database.class.php';
require_once 'system/classes/data_importer.class.php';
require_once 'system/classes/table_config_manager.class.php';
require_once 'system/functions/database.php';
require_once 'system/functions/functions.php';

use system\database;
use system\data_importer;
use system\table_config_manager;

echo "<h1>Enhanced vs Regular Import Comparison</h1>\n";

// Create sample CSV data
$sample_csv_content = "name,age,email,salary,hire_date,is_active\n";
$sample_csv_content .= "<PERSON>,30,<EMAIL>,50000.00,2023-01-15,true\n";
$sample_csv_content .= "<PERSON>,25,<EMAIL>,45000.50,2023-02-20,true\n";
$sample_csv_content .= "Bob <PERSON>,35,<EMAIL>,60000.75,2023-03-10,false\n";

$test_csv_file = tempnam(sys_get_temp_dir(), 'comparison_test_') . '.csv';
file_put_contents($test_csv_file, $sample_csv_content);

echo "<h2>Sample CSV Data:</h2>\n";
echo "<pre>" . htmlspecialchars($sample_csv_content) . "</pre>\n";

$timestamp = time();
$regular_table = "regular_import_test_{$timestamp}";
$enhanced_table = "enhanced_import_test_{$timestamp}";

try {
    echo "<h2>Test 1: Regular Import (JSON Storage)</h2>\n";
    
    // First ensure the regular table exists using hilt method
    if (!database::tableExists($regular_table)) {
        // Create regular hilt table structure
        $create_regular = "CREATE TABLE `{$regular_table}` (
            `id` int(10) UNSIGNED NOT NULL AUTO_INCREMENT,
            `data_json` longtext CHARACTER SET utf8mb4 COLLATE utf8mb4_bin DEFAULT NULL,
            `data_hash` varchar(64) DEFAULT NULL,
            `created_at` timestamp NOT NULL DEFAULT current_timestamp(),
            `updated_at` timestamp NOT NULL DEFAULT current_timestamp() ON UPDATE current_timestamp(),
            PRIMARY KEY (`id`)
        ) ENGINE=InnoDB DEFAULT CHARSET=utf8";
        
        database::rawQuery($create_regular);
        echo "✅ Created regular table structure\n";
    }
    
    $regular_result = data_importer::import_csv_to_hilt_table($regular_table, $test_csv_file, true, false);
    
    if (isset($regular_result['error'])) {
        echo "❌ Regular import failed: " . $regular_result['error'] . "\n";
    } else {
        echo "✅ Regular import successful!\n";
        
        // Check table structure
        $describe_regular = "DESCRIBE `{$regular_table}`";
        $regular_columns = database::rawQuery($describe_regular)->fetchAll();
        
        echo "<h3>Regular Table Structure:</h3>\n";
        echo "<table border='1' style='border-collapse: collapse;'>\n";
        echo "<tr><th>Column</th><th>Type</th><th>Null</th><th>Key</th></tr>\n";
        foreach ($regular_columns as $col) {
            echo "<tr><td>{$col['Field']}</td><td>{$col['Type']}</td><td>{$col['Null']}</td><td>{$col['Key']}</td></tr>\n";
        }
        echo "</table>\n";
        
        // Check sample data
        $regular_data = database::table($regular_table)->limit(1)->get();
        if (!empty($regular_data)) {
            echo "<h3>Regular Table Sample Data:</h3>\n";
            echo "<pre>" . print_r($regular_data[0], true) . "</pre>\n";
        }
    }
    
    echo "<h2>Test 2: Enhanced Import (Typed Columns)</h2>\n";
    
    $enhanced_result = data_importer::import_csv_with_auto_schema($test_csv_file, $enhanced_table, true, false);
    
    if (isset($enhanced_result['error'])) {
        echo "❌ Enhanced import failed: " . $enhanced_result['error'] . "\n";
    } else {
        echo "✅ Enhanced import successful!\n";
        
        // Check table structure
        $describe_enhanced = "DESCRIBE `{$enhanced_table}`";
        $enhanced_columns = database::rawQuery($describe_enhanced)->fetchAll();
        
        echo "<h3>Enhanced Table Structure:</h3>\n";
        echo "<table border='1' style='border-collapse: collapse;'>\n";
        echo "<tr><th>Column</th><th>Type</th><th>Null</th><th>Key</th></tr>\n";
        foreach ($enhanced_columns as $col) {
            echo "<tr><td>{$col['Field']}</td><td>{$col['Type']}</td><td>{$col['Null']}</td><td>{$col['Key']}</td></tr>\n";
        }
        echo "</table>\n";
        
        // Check sample data
        $enhanced_data = database::table($enhanced_table)->limit(1)->get();
        if (!empty($enhanced_data)) {
            echo "<h3>Enhanced Table Sample Data:</h3>\n";
            echo "<pre>" . print_r($enhanced_data[0], true) . "</pre>\n";
        }
        
        // Check if configuration was stored
        $stored_config = table_config_manager::get_table_config($enhanced_table);
        if ($stored_config) {
            echo "<h3>Stored Configuration:</h3>\n";
            echo "<p>✅ Configuration stored successfully</p>\n";
            echo "<p>Route Key: " . $stored_config['route_key'] . "</p>\n";
            echo "<p>Data Source: " . $stored_config['data_source'] . "</p>\n";
            echo "<p>Columns in config: " . count($stored_config['config']['columns']) . "</p>\n";
        } else {
            echo "<p>❌ No stored configuration found</p>\n";
        }
    }
    
    echo "<h2>Comparison Summary</h2>\n";
    
    if (database::tableExists($regular_table) && database::tableExists($enhanced_table)) {
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>\n";
        echo "<tr><th>Feature</th><th>Regular Import</th><th>Enhanced Import</th></tr>\n";
        
        $regular_cols = database::rawQuery("DESCRIBE `{$regular_table}`")->fetchAll();
        $enhanced_cols = database::rawQuery("DESCRIBE `{$enhanced_table}`")->fetchAll();
        
        echo "<tr><td>Number of Columns</td><td>" . count($regular_cols) . "</td><td>" . count($enhanced_cols) . "</td></tr>\n";
        echo "<tr><td>Data Storage</td><td>JSON in single column</td><td>Individual typed columns</td></tr>\n";
        echo "<tr><td>Query Performance</td><td>Slower (JSON parsing)</td><td>Faster (direct column access)</td></tr>\n";
        echo "<tr><td>Filtering</td><td>Limited (JSON functions)</td><td>Full SQL support</td></tr>\n";
        echo "<tr><td>Indexing</td><td>Limited</td><td>Full column indexing</td></tr>\n";
        
        $regular_config = table_config_manager::get_table_config($regular_table);
        $enhanced_config = table_config_manager::get_table_config($enhanced_table);
        
        echo "<tr><td>Stored Configuration</td><td>" . ($regular_config ? 'Yes' : 'No') . "</td><td>" . ($enhanced_config ? 'Yes' : 'No') . "</td></tr>\n";
        echo "</table>\n";
    }
    
    echo "<h2>Testing Table Existence Checks</h2>\n";
    
    $tables_to_check = [$regular_table, $enhanced_table];
    foreach ($tables_to_check as $table) {
        $exists = database::tableExists($table);
        echo "<p>Table '{$table}': " . ($exists ? '✅ EXISTS' : '❌ NOT EXISTS') . "</p>\n";
    }
    
} catch (Exception $e) {
    echo "<h2>❌ Error during testing:</h2>\n";
    echo "<p>" . $e->getMessage() . "</p>\n";
    echo "<pre>" . $e->getTraceAsString() . "</pre>\n";
} finally {
    // Cleanup
    echo "<h2>Cleanup</h2>\n";
    try {
        if (database::tableExists($regular_table)) {
            database::rawQuery("DROP TABLE `{$regular_table}`");
            echo "<p>✅ Cleaned up regular table</p>\n";
        }
        if (database::tableExists($enhanced_table)) {
            database::rawQuery("DROP TABLE `{$enhanced_table}`");
            echo "<p>✅ Cleaned up enhanced table</p>\n";
        }
        unlink($test_csv_file);
        echo "<p>✅ Cleaned up temporary CSV file</p>\n";
    } catch (Exception $e) {
        echo "<p>⚠️ Cleanup error: " . $e->getMessage() . "</p>\n";
    }
}

echo "<h2>Key Takeaways</h2>\n";
echo "<ul>\n";
echo "<li>Regular import stores data as JSON in a single column</li>\n";
echo "<li>Enhanced import creates individual typed columns for better performance</li>\n";
echo "<li>Enhanced import includes automatic configuration storage</li>\n";
echo "<li>Table existence checks should work for both types</li>\n";
echo "</ul>\n";
?>
