<?php
/**
 * Test script to debug table existence checking
 */

// Define constants needed
define('DEBUG_MODE', true);
define('FS_SYS_LOGS', 'system/logs/');
define('FS_SYSTEM', __DIR__ . '/system/');
define('FS_APP_ROOT', __DIR__ . '/');
define('DOMAIN', 'localhost');

// Include necessary files
require_once 'system/classes/database.class.php';
require_once 'system/functions/database.php';
require_once 'system/functions/functions.php';

use system\database;

echo "<h1>Table Existence Check Debug</h1>\n";

// Test the specific table that was reported as not existing
$test_table_name = 'autobooks_test010725010725857_data';

echo "<h2>Testing table: {$test_table_name}</h2>\n";

try {
    echo "<h3>Method 1: database::tableExists()</h3>\n";
    $exists1 = database::tableExists($test_table_name);
    echo "<p>Result: " . ($exists1 ? '✅ EXISTS' : '❌ NOT EXISTS') . "</p>\n";
    
    echo "<h3>Method 2: SHOW TABLES LIKE query</h3>\n";
    $query = "SHOW TABLES LIKE ?";
    $stmt = database::rawQuery($query, [$test_table_name]);
    $result = $stmt->fetchAll();
    echo "<p>Row count: " . count($result) . "</p>\n";
    echo "<p>Result: " . (count($result) > 0 ? '✅ EXISTS' : '❌ NOT EXISTS') . "</p>\n";
    if (count($result) > 0) {
        echo "<p>Found table: " . print_r($result, true) . "</p>\n";
    }
    
    echo "<h3>Method 3: SHOW TABLES (all tables)</h3>\n";
    $all_tables_query = "SHOW TABLES";
    $all_stmt = database::rawQuery($all_tables_query);
    $all_tables = $all_stmt->fetchAll();
    echo "<p>Total tables in database: " . count($all_tables) . "</p>\n";
    
    $found_in_list = false;
    echo "<p>Looking for '{$test_table_name}' in table list:</p>\n";
    echo "<ul>\n";
    foreach ($all_tables as $table_row) {
        $table_name = array_values($table_row)[0]; // Get first column value
        echo "<li>{$table_name}";
        if ($table_name === $test_table_name) {
            echo " ✅ <strong>FOUND!</strong>";
            $found_in_list = true;
        }
        echo "</li>\n";
    }
    echo "</ul>\n";
    echo "<p>Found in list: " . ($found_in_list ? '✅ YES' : '❌ NO') . "</p>\n";
    
    echo "<h3>Method 4: Direct SELECT test</h3>\n";
    try {
        $select_query = "SELECT 1 FROM `{$test_table_name}` LIMIT 1";
        $select_stmt = database::rawQuery($select_query);
        echo "<p>✅ SELECT query succeeded - table exists</p>\n";
    } catch (Exception $e) {
        echo "<p>❌ SELECT query failed: " . $e->getMessage() . "</p>\n";
    }
    
    echo "<h3>Method 5: Information Schema check</h3>\n";
    try {
        $info_query = "SELECT table_name FROM information_schema.tables WHERE table_schema = DATABASE() AND table_name = ?";
        $info_stmt = database::rawQuery($info_query, [$test_table_name]);
        $info_result = $info_stmt->fetchAll();
        echo "<p>Information schema result count: " . count($info_result) . "</p>\n";
        echo "<p>Result: " . (count($info_result) > 0 ? '✅ EXISTS' : '❌ NOT EXISTS') . "</p>\n";
        if (count($info_result) > 0) {
            echo "<p>Found: " . print_r($info_result, true) . "</p>\n";
        }
    } catch (Exception $e) {
        echo "<p>❌ Information schema query failed: " . $e->getMessage() . "</p>\n";
    }
    
    echo "<h3>Method 6: Test with different table names</h3>\n";
    $test_tables = [
        'autobooks_test010725010725857_data',
        'users',
        'nonexistent_table_12345'
    ];
    
    foreach ($test_tables as $table) {
        $exists = database::tableExists($table);
        echo "<p>Table '{$table}': " . ($exists ? '✅ EXISTS' : '❌ NOT EXISTS') . "</p>\n";
    }
    
    echo "<h2>Database Connection Info</h2>\n";
    try {
        $db_query = "SELECT DATABASE() as current_db";
        $db_stmt = database::rawQuery($db_query);
        $db_result = $db_stmt->fetch();
        echo "<p>Current database: " . $db_result['current_db'] . "</p>\n";
    } catch (Exception $e) {
        echo "<p>❌ Could not get database name: " . $e->getMessage() . "</p>\n";
    }
    
} catch (Exception $e) {
    echo "<h2>❌ Error during testing:</h2>\n";
    echo "<p>" . $e->getMessage() . "</p>\n";
    echo "<pre>" . $e->getTraceAsString() . "</pre>\n";
}

echo "<h2>Summary</h2>\n";
echo "<p>This test helps debug why table existence checks might be failing.</p>\n";
echo "<p>If the table shows up in SHOW TABLES but tableExists() returns false, there might be an issue with the method implementation.</p>\n";
?>
