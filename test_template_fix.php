<?php
/**
 * Test script to verify the template fix for the exists() method
 */

// Define constants needed
define('DEBUG_MODE', true);
define('FS_SYS_LOGS', 'system/logs/');
define('FS_SYSTEM', __DIR__ . '/system/');
define('FS_APP_ROOT', __DIR__ . '/');
define('DOMAIN', 'localhost');

// Include necessary files
require_once 'system/classes/database.class.php';
require_once 'system/functions/database.php';
require_once 'system/functions/functions.php';

use system\database;

echo "<h1>Template Fix Verification Test</h1>\n";

// Simulate the exact code that was failing in the template
$table_name = 'test_table_' . time();

echo "<h2>Testing the exact scenario that was failing</h2>\n";

try {
    echo "<p>Testing table existence check for: {$table_name}</p>\n";
    
    // This is the exact line that was causing the TypeError
    $table_exists = database::tableExists($table_name);
    
    if ($table_exists) {
        echo "<p>✅ Table exists: {$table_name}</p>\n";
    } else {
        echo "<p>✅ Table does not exist: {$table_name} (expected)</p>\n";
    }
    
    echo "<p>✅ No TypeError thrown - fix is working!</p>\n";
    
} catch (TypeError $e) {
    echo "<p>❌ TypeError still occurring: " . $e->getMessage() . "</p>\n";
    echo "<pre>" . $e->getTraceAsString() . "</pre>\n";
} catch (Exception $e) {
    echo "<p>⚠️ Other exception: " . $e->getMessage() . "</p>\n";
}

echo "<h2>Testing exists() method directly</h2>\n";

try {
    // Test the exists() method that was causing the issue
    $exists_result = database::table('users')->exists();
    echo "<p>✅ exists() method works: " . ($exists_result ? 'true' : 'false') . "</p>\n";
    
} catch (TypeError $e) {
    echo "<p>❌ TypeError in exists() method: " . $e->getMessage() . "</p>\n";
} catch (Exception $e) {
    echo "<p>⚠️ Other exception in exists(): " . $e->getMessage() . "</p>\n";
}

echo "<h2>Testing method chaining</h2>\n";

try {
    // Test method chaining which might reveal type issues
    $chained_result = database::table('users')
        ->select('id')
        ->where('id', '>', 0)
        ->limit(1)
        ->exists();
    
    echo "<p>✅ Method chaining with exists() works: " . ($chained_result ? 'true' : 'false') . "</p>\n";
    
} catch (TypeError $e) {
    echo "<p>❌ TypeError in method chaining: " . $e->getMessage() . "</p>\n";
} catch (Exception $e) {
    echo "<p>⚠️ Other exception in chaining: " . $e->getMessage() . "</p>\n";
}

echo "<h2>Testing template-like scenario</h2>\n";

// Simulate the template code more closely
$test_table_name = 'autobooks_enhanced_data';

try {
    // Check if table exists using the new helper method
    $table_exists = database::tableExists($test_table_name);
    
    if (!$table_exists) {
        $error_message = "Table '{$test_table_name}' does not exist. Please upload CSV data to create it.";
        echo "<p>✅ Template scenario works: {$error_message}</p>\n";
    } else {
        echo "<p>✅ Template scenario works: Table exists</p>\n";
        
        // Try to get some data like the template would
        try {
            $sample_data = database::table($test_table_name)->limit(1)->get();
            echo "<p>✅ Data retrieval works: " . count($sample_data) . " records</p>\n";
        } catch (Exception $e) {
            echo "<p>⚠️ Data retrieval issue: " . $e->getMessage() . "</p>\n";
        }
    }
    
} catch (TypeError $e) {
    echo "<p>❌ TypeError in template scenario: " . $e->getMessage() . "</p>\n";
} catch (Exception $e) {
    echo "<p>⚠️ Other exception in template scenario: " . $e->getMessage() . "</p>\n";
}

echo "<h2>Summary</h2>\n";
echo "<p>This test verifies that:</p>\n";
echo "<ul>\n";
echo "<li>✅ database::tableExists() works without TypeError</li>\n";
echo "<li>✅ QueryBuilder::exists() works without TypeError</li>\n";
echo "<li>✅ Method chaining with exists() works properly</li>\n";
echo "<li>✅ Template-like scenarios work as expected</li>\n";
echo "</ul>\n";

echo "<p><strong>The TypeError: Cannot assign array to property system\\database::\$select of type string should be fixed!</strong></p>\n";
?>
